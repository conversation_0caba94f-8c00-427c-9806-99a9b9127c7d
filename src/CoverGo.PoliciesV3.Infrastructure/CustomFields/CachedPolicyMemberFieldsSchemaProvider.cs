using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Caching;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Infrastructure.Caching.Dtos;
using CoverGo.PoliciesV3.Infrastructure.Caching.Extensions;

namespace CoverGo.PoliciesV3.Infrastructure.CustomFields;

/// <summary>
/// Cached decorator for IPolicyMemberFieldsSchemaProvider that provides distributed caching
/// for schema data to improve performance and reduce external service calls.
/// </summary>
public class CachedPolicyMemberFieldsSchemaProvider(
    ITenantProvider tenantProvider,
    IPolicyMemberFieldsSchemaProvider inner,
    CacheProvider cache) : IPolicyMemberFieldsSchemaProvider
{
    #region Cache Configuration

    private const string CustomFieldsSchemaCacheKeyTemplate = "schema:custom-fields:{tenant}:{contractHolderId}:{productId}";
    private const string MemberUploadSchemaCacheKeyTemplate = "schema:upload:{tenant}:{contractHolderId}:{productId}:{endorsementId}";

    /// <summary>
    /// Cache TTL for schema data - 6 hours to match other schema caching patterns
    /// Schema data is relatively static and can be cached for longer periods
    /// </summary>
    private static readonly TimeSpan SchemaCacheTtl = TimeSpan.FromHours(6);

    #endregion

    #region IPolicyMemberFieldsSchemaProvider Implementation

    /// <summary>
    /// Gets the base custom fields schema for a specific policy configuration with caching.
    /// </summary>
    public async Task<PolicyMemberFieldsSchema> GetCustomFieldsSchema(
        string? contractHolderId,
        ProductId productId,
        CancellationToken cancellationToken = default)
    {
        if (!tenantProvider.TryGetCurrent(out TenantId? tenantId))
        {
            throw new InvalidOperationException("Current tenant is not set.");
        }

        string cacheKey = GetCustomFieldsSchemaCacheKey(tenantId.Value, contractHolderId, productId);

        // Try to get from cache first
        PolicyMemberFieldsSchemaDto? cachedSchemaDto = null;
        if (cachedSchemaDto != null)
        {
            return cachedSchemaDto.ToDomain();
        }

        // Not in cache, fetch from inner provider
        PolicyMemberFieldsSchema schema = await inner.GetCustomFieldsSchema(contractHolderId, productId, cancellationToken);

        // Cache the DTO instead of the domain object to avoid interface serialization issues
        PolicyMemberFieldsSchemaDto schemaDto = schema.ToDto();
        await cache.SetAsync(cacheKey, schemaDto, SchemaCacheTtl, cancellationToken);

        return schema;
    }

    /// <summary>
    /// Gets the schema with upload-specific processing applied with caching.
    /// </summary>
    public async Task<PolicyMemberFieldsSchema> GetMemberUploadSchema(
        string? contractHolderId,
        ProductId productId,
        EndorsementId? endorsementId,
        CancellationToken cancellationToken = default)
    {
        if (!tenantProvider.TryGetCurrent(out TenantId? tenantId))
        {
            throw new InvalidOperationException("Current tenant is not set.");
        }

        string cacheKey = GetMemberUploadSchemaCacheKey(tenantId.Value, contractHolderId, productId, endorsementId);

        // Try to get from cache first
        PolicyMemberFieldsSchemaDto? cachedSchemaDto = null;
        if (cachedSchemaDto != null)
        {
            return cachedSchemaDto.ToDomain();
        }

        // Not in cache, fetch from inner provider
        PolicyMemberFieldsSchema schema = await inner.GetMemberUploadSchema(contractHolderId, productId, endorsementId, cancellationToken);

        // Cache the DTO instead of the domain object to avoid interface serialization issues
        PolicyMemberFieldsSchemaDto schemaDto = schema.ToDto();
        await cache.SetAsync(cacheKey, schemaDto, SchemaCacheTtl, cancellationToken);

        return schema;
    }

    #endregion

    #region Private Helper Methods

    /// <summary>
    /// Generates cache key for custom fields schema following established patterns.
    /// Format: schema:custom-fields:{tenant}:{contractHolderId}:{productId}
    /// </summary>
    private static string GetCustomFieldsSchemaCacheKey(string tenantId, string? contractHolderId, ProductId productId)
        => CustomFieldsSchemaCacheKeyTemplate
            .Replace("{tenant}", tenantId)
            .Replace("{contractHolderId}", contractHolderId ?? "null")
            .Replace("{productId}", productId.ToString());

    /// <summary>
    /// Generates cache key for member upload schema following established patterns.
    /// Format: schema:upload:{tenant}:{contractHolderId}:{productId}:{endorsementId}
    /// </summary>
    private static string GetMemberUploadSchemaCacheKey(string tenantId, string? contractHolderId, ProductId productId, EndorsementId? endorsementId)
        => MemberUploadSchemaCacheKeyTemplate
            .Replace("{tenant}", tenantId)
            .Replace("{contractHolderId}", contractHolderId ?? "null")
            .Replace("{productId}", productId.ToString())
            .Replace("{endorsementId}", endorsementId?.ToString() ?? "null");

    #endregion
}
