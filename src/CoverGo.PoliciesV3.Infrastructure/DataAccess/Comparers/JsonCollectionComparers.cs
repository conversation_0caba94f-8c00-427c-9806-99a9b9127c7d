using System.Text.Json;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Infrastructure.Common.Helpers;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess.Comparers;

public static class JsonCollectionComparers
{
    #region Generic Comparer Factory

    public static ValueComparer<T> Create<T>()
    {
        return new ValueComparer<T>(
            // Equals delegate: Compare objects by their JSON representation
            (left, right) => CompareByJson(left, right),

            // Hash code delegate: Generate hash based on JSON representation
            obj => GetJsonHashCode(obj),

            // Snapshot delegate: Create deep copy via JSON serialization/deserialization
            source => CreateDeepCopy(source)
        );
    }

    #endregion

    #region Pre-configured Static Comparers

    public static readonly ValueComparer<ICollection<PolicyField>> PolicyFieldCollection = Create<ICollection<PolicyField>>();

    public static readonly ValueComparer<ICollection<MemberLoading>?> MemberLoadingCollection = Create<ICollection<MemberLoading>?>();

    public static readonly ValueComparer<ICollection<MemberBenefitsLoading>?> MemberBenefitsLoadingCollection = Create<ICollection<MemberBenefitsLoading>?>();

    public static readonly ValueComparer<ICollection<MemberBenefitsUnderwriting>?> MemberBenefitsUnderwritingCollection = Create<ICollection<MemberBenefitsUnderwriting>?>();

    #endregion

    #region Private Implementation Methods

    /// <summary>
    /// Compares two objects for equality using their JSON representation.
    /// Handles null values appropriately and uses JsonSchemaProcessor.DatabaseOptions for consistent serialization.
    /// </summary>
    /// <typeparam name="T">The type of objects to compare</typeparam>
    /// <param name="left">The first object to compare</param>
    /// <param name="right">The second object to compare</param>
    /// <returns>True if the objects are equal based on their JSON representation, false otherwise</returns>
    private static bool CompareByJson<T>(T? left, T? right)
    {
        // Handle null cases
        if (left is null && right is null) return true;
        if (left is null || right is null) return false;

        try
        {
            // Serialize both objects using the same options used for database storage
            string leftJson = JsonSerializer.Serialize(left, JsonSchemaProcessor.DatabaseOptions);
            string rightJson = JsonSerializer.Serialize(right, JsonSchemaProcessor.DatabaseOptions);

            // Compare JSON strings for deep equality
            return string.Equals(leftJson, rightJson, StringComparison.Ordinal);
        }
        catch (JsonException)
        {
            // If serialization fails, fall back to reference equality
            return ReferenceEquals(left, right);
        }
    }

    /// <summary>
    /// Generates a stable hash code for an object based on its JSON representation.
    /// Uses JsonSchemaProcessor.DatabaseOptions for consistent serialization.
    /// </summary>
    /// <typeparam name="T">The type of object to generate hash code for</typeparam>
    /// <param name="obj">The object to generate hash code for</param>
    /// <returns>A stable hash code based on the object's JSON representation</returns>
    private static int GetJsonHashCode<T>(T? obj)
    {
        if (obj is null) return 0;

        try
        {
            // Serialize object using the same options used for database storage
            string json = JsonSerializer.Serialize(obj, JsonSchemaProcessor.DatabaseOptions);

            // Use the JSON string's hash code for stable hashing
            return json.GetHashCode(StringComparison.Ordinal);
        }
        catch (JsonException)
        {
            // If serialization fails, fall back to object's hash code
            return obj.GetHashCode();
        }
    }

    /// <summary>
    /// Creates a deep copy of an object using JSON serialization/deserialization.
    /// Uses JsonSchemaProcessor.DatabaseOptions for consistent serialization.
    /// </summary>
    /// <typeparam name="T">The type of object to create a deep copy of</typeparam>
    /// <param name="source">The source object to copy</param>
    /// <returns>A deep copy of the source object, or the original if copying fails</returns>
    private static T CreateDeepCopy<T>(T source)
    {
        if (source is null) return source;

        try
        {
            // Serialize and deserialize using the same options used for database storage
            string json = JsonSerializer.Serialize(source, JsonSchemaProcessor.DatabaseOptions);
            T? copy = JsonSerializer.Deserialize<T>(json, JsonSchemaProcessor.DatabaseOptions);

            // Return the copy, or the original if deserialization returned null
            return copy ?? source;
        }
        catch (JsonException)
        {
            // If serialization/deserialization fails, return the original object
            // This maintains Entity Framework functionality even if deep copying fails
            return source;
        }
    }

    #endregion
}
