﻿using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Constants;
using CoverGo.Multitenancy;
using CoverGo.Policies.Client;
using CoverGo.PoliciesV3.Application.Mapping.LegacyPolicyMapping;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Policies;
using Microsoft.Extensions.Logging;
using LegacyPolicy = CoverGo.Policies.Client.Policy;

namespace CoverGo.PoliciesV3.Infrastructure.Policies;

public class LegacyPolicyService(
    HttpClient httpClient,
    TenantId tenantId,
    ILogger<LegacyPolicyService> logger) : ILegacyPolicyService
{
    private readonly PoliciesClient _policiesRestClient = new(httpClient);

    #region Single Policy Operations

    private async Task<LegacyPolicy?> GetLegacyPolicyById(string policyId, AsOf? asOf, CancellationToken cancellationToken) =>
        await _policiesRestClient.Policy_GetPolicyAsync(
                tenantId.Value,
                policyId,
                asOf,
                cancellationToken);

    public async Task<PolicyDto?> GetPolicyById(string policyId, CancellationToken cancellationToken)
    {
        var asOf = new AsOf();
        LegacyPolicy? legacyPolicy = await GetLegacyPolicyById(policyId, asOf, cancellationToken);
        return legacyPolicy != null ? MapSafely(legacyPolicy, policyId) : null;
    }

    #endregion

    #region Batch Operations - Optimized

    public async Task<List<string>> GetIdsByContractHolderId(string? contractHolderId, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(contractHolderId))
            return [];

        try
        {
            var queryArguments = new QueryArgumentsOfPolicyWhere
            {
                Where = new PolicyWhere { ContractHolder = new EntityWhere { Id = contractHolderId } },
                First = ConfigurationConstants.QueryLimits.MaxPolicyIdsPerQuery
            };

            List<string>? policyIds = await _policiesRestClient.Policy_GetPolicyIdsAsync(
                tenantId.Value, queryArguments, cancellationToken);

            int resultCount = policyIds?.Count ?? 0;
            logger.LogDebug("Retrieved {PolicyCount} policy IDs for contract holder {ContractHolderId}",
                resultCount, contractHolderId);

            return policyIds ?? [];
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to get policy IDs for contract holder {ContractHolderId}", contractHolderId);
            return [];
        }
    }

    public async Task<List<LegacyPolicy>> GetLegacyPoliciesByIds(List<string> policyIds, CancellationToken cancellationToken)
    {
        if (policyIds.Count == 0)
            return [];

        try
        {
            var queryArguments = new QueryArgumentsOfPolicyWhere
            {
                Where = new PolicyWhere { Id_in = policyIds },
                First = Math.Min(policyIds.Count, ConfigurationConstants.QueryLimits.MaxPoliciesPerBatchQuery)
            };

            List<LegacyPolicy>? policies = await _policiesRestClient.Policy_GetPoliciesAsync(
                tenantId.Value, queryArguments, cancellationToken);

            int resultCount = policies?.Count ?? 0;
            logger.LogDebug("Retrieved {PolicyCount} policies out of {RequestedCount} requested",
                resultCount, policyIds.Count);

            return policies ?? [];
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to get policies by IDs. Requested: {PolicyIds}", string.Join(", ", policyIds));
            throw;
        }
    }

    public async Task<List<PolicyDto>> GetPolicyDtosByIds(List<string> policyIds, CancellationToken cancellationToken)
    {
        if (policyIds.Count == 0)
            return [];

        List<LegacyPolicy> legacyPolicies = await GetLegacyPoliciesByIds(policyIds, cancellationToken);

        var policyDtos = new List<PolicyDto>(legacyPolicies.Count);

        if (legacyPolicies.Count > ValidationConstants.BatchProcessing.ParallelThreshold)
        {
            var mappedPolicies = legacyPolicies
                .AsParallel()
                .WithCancellation(cancellationToken)
                .Select(policy => MapSafely(policy, policy.Id!))
                .Where(dto => dto != null)
                .Cast<PolicyDto>()
                .ToList();

            policyDtos.AddRange(mappedPolicies);
        }
        else
        {
            var mappedPolicies = legacyPolicies
                .Select(legacyPolicy => MapSafely(legacyPolicy, legacyPolicy.Id!))
                .Where(dto => dto != null)
                .Cast<PolicyDto>()
                .ToList();

            policyDtos.AddRange(mappedPolicies);
        }

        logger.LogDebug("Successfully mapped {MappedCount} out of {TotalCount} policies to DTOs",
            policyDtos.Count, legacyPolicies.Count);

        return policyDtos;
    }

    #endregion

    #region Private Helpers - Centralized Error Handling

    /// <summary>
    /// Maps legacy policy to DTO with centralized error handling and logging.
    /// Returns null instead of throwing to allow graceful degradation in batch operations.
    /// </summary>
    private PolicyDto? MapSafely(LegacyPolicy legacyPolicy, string policyId)
    {
        try
        {
            return PolicyDtoMapper.MapToDto(legacyPolicy);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to map policy {PolicyId} to DTO. Policy will be skipped.", policyId);
            return null; // Return null instead of throwing - allows batch operations to continue
        }
    }

    #endregion
}