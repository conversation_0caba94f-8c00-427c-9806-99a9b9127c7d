using System.Diagnostics;
using System.Reflection;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Common.Providers;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Products;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Tests.Unit.Common;
using CoverGo.Users.Client;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.ValidateUpload;

/// <summary>
/// Realistic performance tests for the validation pipeline in ValidatePolicyMemberUploadHandler.
/// Tests actual validation logic with minimal mocking to get accurate performance measurements.
/// </summary>
public class ValidatePolicyMemberUploadHandlerRealisticPerformanceTests : IDisposable
{
    #region Test Infrastructure Setup

    private readonly Mock<IPolicyMemberUploadRepository> _mockUploadRepository;
    private readonly Mock<IPolicyMemberUploadValidationErrorExporterService> _mockValidationErrorExporterService;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IFileProcessingService> _mockFileProcessingService;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<ILogger<ValidatePolicyMemberUploadHandler>> _mockLogger;
    private readonly Mock<IMemoryCache> _mockMemoryCache;

    // Real validation components
    private readonly CompleteUploadValidationSpecification _realCompleteValidationSpec;
    private readonly ValidatePolicyMemberUploadHandler _handler;
    private readonly TenantId _tenantId;
    private readonly List<PerformanceMetric> _performanceMetrics;

    public ValidatePolicyMemberUploadHandlerRealisticPerformanceTests()
    {
        // Initialize mocks for external dependencies only
        _mockUploadRepository = new Mock<IPolicyMemberUploadRepository>();
        _mockValidationErrorExporterService = new Mock<IPolicyMemberUploadValidationErrorExporterService>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockFileProcessingService = new Mock<IFileProcessingService>();
        _mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockProductService = new Mock<IProductService>();
        _mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        _mockUsersService = new Mock<IUsersService>();
        _mockLogger = new Mock<ILogger<ValidatePolicyMemberUploadHandler>>();
        _mockMemoryCache = new Mock<IMemoryCache>();

        _performanceMetrics = [];
        _tenantId = new TenantId("test-tenant");

        // Create REAL validation specifications with real logic
        _realCompleteValidationSpec = CreateRealCompleteValidationSpecification();

        // Setup handler with all required dependencies
        _handler = CreateHandlerWithRealValidation();

        // Setup default mocks for external services only
        SetupExternalServiceMocks();
    }

    #endregion

    #region Performance Benchmarks

    [Fact]
    [Trait("Category", TestCategories.Performance)]
    public async Task ValidationPipeline_SmallDataset_ShouldMeetRealisticTargets()
    {
        // Arrange
        const int memberCount = 100;
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        SetupRealisticValidationScenario(memberCount, errorRate: 0.05); // 5% error rate

        // Act & Measure
        PerformanceMetric metric = await MeasureValidationPerformance(command, "SmallDataset_Realistic");

        // Assert - More realistic expectations
        metric.ExecutionTime.Should().BeLessThan(TimeSpan.FromSeconds(5),
            "small datasets should validate within 5 seconds with real validation logic");
        metric.MemoryUsed.Should().BeLessThan(50 * 1024 * 1024,
            "small datasets should use less than 50MB");
        metric.ThroughputPerSecond.Should().BeGreaterThan(20,
            "should process at least 20 members per second with full validation");
    }

    [Fact]
    [Trait("Category", TestCategories.Performance)]
    public async Task ValidationPipeline_MediumDataset_ShouldMeetRealisticTargets()
    {
        // Arrange
        const int memberCount = 1000;
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        SetupRealisticValidationScenario(memberCount, errorRate: 0.1); // 10% error rate

        // Act & Measure
        PerformanceMetric metric = await MeasureValidationPerformance(command, "MediumDataset_Realistic");

        // Assert - More realistic expectations
        metric.ExecutionTime.Should().BeLessThan(TimeSpan.FromSeconds(30),
            "medium datasets should validate within 30 seconds with real validation logic");
        metric.MemoryUsed.Should().BeLessThan(100 * 1024 * 1024,
            "medium datasets should use less than 100MB");
        metric.ThroughputPerSecond.Should().BeGreaterThan(30,
            "should process at least 30 members per second with full validation");
    }

    [Fact]
    [Trait("Category", TestCategories.Performance)]
    public async Task ValidationPipeline_LargeDataset_ShouldMeetRealisticTargets()
    {
        // Arrange
        const int memberCount = 5000;
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        SetupRealisticValidationScenario(memberCount, errorRate: 0.15); // 15% error rate

        // Act & Measure
        PerformanceMetric metric = await MeasureValidationPerformance(command, "LargeDataset_Realistic");

        // Assert - More realistic expectations
        metric.ExecutionTime.Should().BeLessThan(TimeSpan.FromMinutes(3),
            "large datasets should validate within 3 minutes with real validation logic");
        metric.MemoryUsed.Should().BeLessThan(200 * 1024 * 1024,
            "large datasets should use less than 200MB");
        metric.ThroughputPerSecond.Should().BeGreaterThan(25,
            "should maintain at least 25 members per second throughput for large datasets");
    }

    [Fact]
    [Trait("Category", TestCategories.Performance)]
    public async Task ValidationPipeline_WithHighErrorRate_ShouldMaintainPerformance()
    {
        // Arrange
        const int memberCount = 1000;
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        SetupRealisticValidationScenario(memberCount, errorRate: 0.5); // 50% error rate

        // Act & Measure
        PerformanceMetric metric = await MeasureValidationPerformance(command, "HighErrorRate_Realistic");

        // Assert
        metric.ExecutionTime.Should().BeLessThan(TimeSpan.FromMinutes(1),
            "validation with high error rates should complete within 1 minute");
        metric.ThroughputPerSecond.Should().BeGreaterThan(15,
            "should maintain reasonable throughput even with many errors");
    }

    #endregion

    #region Concurrent Performance Tests

    [Fact]
    [Trait("Category", TestCategories.Performance)]
    public async Task ValidationPipeline_ConcurrentRequests_ShouldScaleReasonably()
    {
        // Arrange
        const int memberCount = 500;
        const int concurrentRequests = 3; // Reduced for realistic testing

        var commands = Enumerable.Range(0, concurrentRequests)
            .Select(_ => CreateValidCommand())
            .ToList();

        foreach (ValidatePolicyMemberUploadCommand command in commands)
        {
            SetupRealisticValidationScenario(memberCount, errorRate: 0.1);
        }

        // Act & Measure
        var stopwatch = Stopwatch.StartNew();
        GC.GetTotalMemory(forceFullCollection: true);

        Task<Result<ValidatePolicyMemberUploadResponse>>[] tasks = [.. commands.Select(cmd => _handler.Handle(cmd, CancellationToken.None))];

        Result<ValidatePolicyMemberUploadResponse>[] results = await Task.WhenAll(tasks);

        stopwatch.Stop();
        GC.GetTotalMemory(forceFullCollection: false);

        // Assert
        results.Should().AllSatisfy(result => result.IsSuccess.Should().BeTrue());

        double totalMembers = memberCount * concurrentRequests;
        double throughput = totalMembers / stopwatch.Elapsed.TotalSeconds;

        stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromMinutes(2),
            "concurrent requests should complete within 2 minutes");
        throughput.Should().BeGreaterThan(10,
            "concurrent processing should maintain at least 10 members/second throughput");
    }

    #endregion

    #region Real Validation Component Tests

    [Fact]
    [Trait("Category", TestCategories.Performance)]
    public async Task RealUploadWideValidation_ShouldPerformWithinLimits()
    {
        // Arrange
        const int memberCount = 2000;
        UploadWideValidationContext uploadContext = CreateUploadWideValidationContext(memberCount);
        UploadWideValidationSpecification realUploadWideSpec = CreateRealUploadWideValidationSpecification();

        // Act & Measure
        var stopwatch = Stopwatch.StartNew();
        long memoryBefore = GC.GetTotalMemory(forceFullCollection: true);

        BatchValidationResult result = await realUploadWideSpec.ValidateBatchAsync(uploadContext);

        stopwatch.Stop();
        long memoryAfter = GC.GetTotalMemory(forceFullCollection: false);

        // Assert
        result.Should().NotBeNull();
        stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromSeconds(30),
            "upload-wide validation should complete within 30 seconds for 2000 members");

        long memoryUsed = memoryAfter - memoryBefore;
        memoryUsed.Should().BeLessThan(50 * 1024 * 1024,
            "upload-wide validation should use less than 50MB for 2000 members");
    }

    #endregion

    #region Helper Methods for Real Validation Setup

    private CompleteUploadValidationSpecification CreateRealCompleteValidationSpecification()
    {
        // Create real upload-wide validation
        UploadWideValidationSpecification realUploadWideSpec = CreateRealUploadWideValidationSpecification();

        // Create real individual member validation  
        IndividualMemberValidationSpecification realIndividualSpec = CreateRealIndividualMemberValidationSpecification();

        // Create real error aggregator
        var mockAggregatorLogger = new Mock<ILogger<ValidationErrorAggregator>>();
        var realErrorAggregator = new ValidationErrorAggregator(mockAggregatorLogger.Object);

        // Create real complete validation spec logger
        var mockCompleteLogger = new Mock<ILogger<CompleteUploadValidationSpecification>>();

        return new CompleteUploadValidationSpecification(
            realUploadWideSpec,
            realIndividualSpec,
            realErrorAggregator,
            mockCompleteLogger.Object);
    }

    private UploadWideValidationSpecification CreateRealUploadWideValidationSpecification()
    {
        // Create real individual upload validation specs
        var mockEmailLogger = new Mock<ILogger<UploadMustHaveUniqueEmailsSpecification>>();
        var realUniqueEmailsSpec = new UploadMustHaveUniqueEmailsSpecification(mockEmailLogger.Object);

        var mockIdLogger = new Mock<ILogger<UploadMustHaveUniqueIdentificationSpecification>>();
        var realUniqueIdSpec = new UploadMustHaveUniqueIdentificationSpecification(mockIdLogger.Object);

        var mockMemberIdLogger = new Mock<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>();
        var realUniqueMemberIdsSpec = new UploadMustHaveUniqueMemberIdsSpecification(mockMemberIdLogger.Object);

        var mockDependentLogger = new Mock<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>();
        var realDependentPlanSpec = new DependentAndEmployeeMustBeOnSamePlanSpecification(mockDependentLogger.Object);

        // Create real orchestrator
        var mockOrchestratorLogger = new Mock<ILogger<UploadValidationOrchestrator>>();
        var realOrchestrator = new UploadValidationOrchestrator(mockOrchestratorLogger.Object);

        var mockUploadWideLogger = new Mock<ILogger<UploadWideValidationSpecification>>();

        return new UploadWideValidationSpecification(
            realUniqueEmailsSpec,
            realUniqueIdSpec,
            realUniqueMemberIdsSpec,
            realDependentPlanSpec,
            realOrchestrator,
            mockUploadWideLogger.Object);
    }

    private IndividualMemberValidationSpecification CreateRealIndividualMemberValidationSpecification()
    {
        // Create real uniqueness service mock (since it's external)
        var mockUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        SetupUniquenessServiceForPerformance(mockUniquenessService);

        // Create real individual member validation specs
        var mockEmailLogger = new Mock<ILogger<MemberMustHaveUniqueEmailSpecification>>();
        var realUniqueEmailSpec = new MemberMustHaveUniqueEmailSpecification(
            mockUniquenessService.Object, mockEmailLogger.Object);

        var mockHkidLogger = new Mock<ILogger<MemberMustHaveUniqueHKIDSpecification>>();
        var realUniqueHkidSpec = new MemberMustHaveUniqueHKIDSpecification(
            mockUniquenessService.Object, mockHkidLogger.Object);

        var mockPassportLogger = new Mock<ILogger<MemberMustHaveUniquePassportSpecification>>();
        var realUniquePassportSpec = new MemberMustHaveUniquePassportSpecification(
            mockUniquenessService.Object, mockPassportLogger.Object);

        var mockStaffLogger = new Mock<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>();
        var realUniqueStaffSpec = new MemberMustHaveUniqueStaffNumberSpecification(
            mockUniquenessService.Object, mockStaffLogger.Object);

        var mockIdRulesLogger = new Mock<ILogger<MemberIdMustFollowBusinessRulesSpecification>>();
        var realIdRulesSpec = new MemberIdMustFollowBusinessRulesSpecification(mockIdRulesLogger.Object);

        var mockSchemaLogger = new Mock<ILogger<MemberFieldsMustMatchSchemaSpecification>>();
        var realSchemaSpec = new MemberFieldsMustMatchSchemaSpecification(mockSchemaLogger.Object);

        var mockDateLogger = new Mock<ILogger<MemberEffectiveDateMustBeValidSpecification>>();
        var realDateSpec = new MemberEffectiveDateMustBeValidSpecification(mockDateLogger.Object);

        var mockDependentLogger = new Mock<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>();
        var realDependentSpec = new DependentMustHaveValidPrimaryMemberSpecification(
            _mockPolicyMemberQueryService.Object, mockDependentLogger.Object);

        var mockPlanLogger = new Mock<ILogger<MemberMustHaveValidPlanIdSpecification>>();
        var realPlanSpec = new MemberMustHaveValidPlanIdSpecification(mockPlanLogger.Object);

        // Create real concurrent processor
        var realProcessor = new ConcurrentMemberProcessor(maxConcurrentValidations: 10); // Reduced for testing

        var mockIndividualLogger = new Mock<ILogger<IndividualMemberValidationSpecification>>();

        return new IndividualMemberValidationSpecification(
            realUniqueEmailSpec,
            realUniqueHkidSpec,
            realUniquePassportSpec,
            realUniqueStaffSpec,
            realIdRulesSpec,
            realSchemaSpec,
            realDateSpec,
            realDependentSpec,
            realPlanSpec,
            realProcessor,
            mockIndividualLogger.Object);
    }

    private ValidatePolicyMemberUploadHandler CreateHandlerWithRealValidation()
    {
        // Create mock for PolicyMemberValidationDataService
        var mockValidationDataService = new Mock<PolicyMemberValidationDataService>(
            _mockLegacyPolicyService.Object,
            _mockProductService.Object,
            _mockFeatureManager.Object,
            _tenantId,
            _mockSchemaProvider.Object,
            new Mock<ILogger<PolicyMemberValidationDataService>>().Object);

        return new ValidatePolicyMemberUploadHandler(
            _mockUploadRepository.Object,
            _mockValidationErrorExporterService.Object,
            _mockLegacyPolicyService.Object,
            _realCompleteValidationSpec,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            _mockFileProcessingService.Object,
            _mockLogger.Object,
            mockValidationDataService.Object);
    }

    private void SetupExternalServiceMocks()
    {
        // Setup memory cache mock
        var mockCacheEntry = new Mock<ICacheEntry>();
        _mockMemoryCache.Setup(x => x.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);
        _mockMemoryCache.Setup(x => x.TryGetValue(It.IsAny<object>(), out It.Ref<object?>.IsAny)).Returns(false);

        // Setup file processing service with realistic file data
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(
            It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((string _, string _, CancellationToken _) =>
                CreateRealisticFileProcessingResult());

        // Setup feature manager
        _mockFeatureManager.Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);

        // Setup users service with correct return type (HashSet<string>)
        _mockUsersService.Setup(x => x.GetMemberIdsWithoutExistingIndividual(It.IsAny<HashSet<string>>(), It.IsAny<CancellationToken>()))
            .Returns((HashSet<string> memberIds, CancellationToken _) =>
                Task.FromResult(memberIds.Where((_, index) => index % 10 == 0).ToHashSet())); // Return HashSet<string>

        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .Returns((QueryArgumentsOfIndividualWhere args, CancellationToken _) =>
                Task.FromResult(CreateRealisticIndividuals(args)));

        // Setup legacy policy service
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((string _, CancellationToken _) => CreateValidPolicy());

        // Setup schema provider
        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(
            It.IsAny<string>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateRealisticSchema());

        // Setup upload repository
        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMemberUploadId id, CancellationToken _) => CreateUploadWithId(id));

        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
            It.IsAny<PolicyMemberUploadId>(), It.IsAny<PolicyMemberUploadStatus>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Setup policy member query service with realistic data (corrected signatures)
        _mockPolicyMemberQueryService.Setup(x => x.GetActiveMembersAsync(
            It.IsAny<List<PolicyId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(
            It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .Returns((List<string> memberIds, PolicyId _, List<EndorsementId> _, CancellationToken _) =>
                Task.FromResult(CreateRealisticPolicyMembers(memberIds)));

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(
            It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .Returns((List<string> memberIds, CancellationToken _) =>
                Task.FromResult(CreateRealisticValidationStates(memberIds)));

        // Setup product service
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<string> { "BASIC", "PREMIUM", "DELUXE" });

        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("standard");
    }

    private void SetupUniquenessServiceForPerformance(Mock<IPolicyMemberUniquenessService> mockService)
    {
        // Setup with realistic performance characteristics using correct method signatures
        mockService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(), It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()))
            .Returns((PolicyId _, string _, PolicyMemberId? _,
                Dictionary<string, object> _, List<string> fieldNames, List<EndorsementId> _,
                CancellationToken _) =>
            {
                // Simulate some duplication (5% chance)
                var duplicates = fieldNames.Where(_ => Random.Shared.NextDouble() < 0.05).ToList();
                return Task.FromResult(duplicates);
            });

        mockService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(), It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()))
            .Returns((PolicyId _, string _, PolicyMemberId? _,
                Dictionary<string, object> _, List<string> fieldNames, List<EndorsementId> _,
                CancellationToken _) =>
            {
                // Simulate some duplication (3% chance)
                var duplicates = fieldNames.Where(_ => Random.Shared.NextDouble() < 0.03).ToList();
                return Task.FromResult(duplicates);
            });

        mockService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
            It.IsAny<string>(), It.IsAny<PolicyMemberId?>(), It.IsAny<List<PolicyId>>(),
            It.IsAny<List<EndorsementId>>(), It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<CancellationToken>()))
            .Returns((string _, PolicyMemberId? _, List<PolicyId> _,
                List<EndorsementId> _, Dictionary<string, object> _, List<string> fieldNames,
                CancellationToken _) =>
            {
                // Simulate some duplication (1% chance)
                var duplicates = fieldNames.Where(_ => Random.Shared.NextDouble() < 0.01).ToList();
                return Task.FromResult(duplicates);
            });
    }

    private void SetupRealisticValidationScenario(int memberCount, double errorRate)
    {
        // Create realistic member data with actual validation scenarios
        List<Dictionary<string, string?>> memberData = CreateRealisticMemberData(memberCount, errorRate);

        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(
            It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberCount));
    }

    #endregion

    #region Realistic Data Generation

    private FileProcessingResult CreateRealisticFileProcessingResult()
    {
        // Create minimal data for successful file processing
        List<Dictionary<string, string?>> memberData = CreateRealisticMemberData(100, 0.1);
        return FileProcessingResult.Success(memberData, memberData.Count);
    }

    private List<Dictionary<string, string?>> CreateRealisticMemberData(int memberCount, double errorRate)
    {
        var memberData = new List<Dictionary<string, string?>>();
        var random = new Random(42); // Fixed seed for consistent testing

        for (int i = 0; i < memberCount; i++)
        {
            var member = new Dictionary<string, string?>
            {
                ["memberId"] = $"M{i:D6}",
                ["email"] = GenerateEmail(i, errorRate, random),
                ["firstName"] = $"FirstName{i}",
                ["lastName"] = $"LastName{i}",
                ["dateOfBirth"] = GenerateRandomDate(random, DateTimeProvider.UtcNow.AddYears(-65), DateTimeProvider.UtcNow.AddYears(-18)),
                ["planId"] = GeneratePlanId(random),
                ["effectiveDate"] = GenerateRandomDate(random, DateTimeProvider.UtcNow.AddDays(-30), DateTimeProvider.UtcNow.AddDays(30)),
                ["hkid"] = GenerateHKID(i, errorRate, random),
                ["passport"] = GeneratePassport(i, errorRate, random),
                ["staffNumber"] = $"STAFF{i:D4}",
                ["relationship"] = GenerateRelationship(random),
                ["gender"] = random.Next(2) == 0 ? "M" : "F"
            };

            // Introduce some validation errors based on error rate
            if (random.NextDouble() < errorRate)
            {
                IntroduceValidationErrors(member, random);
            }

            memberData.Add(member);
        }

        return memberData;
    }

    private static string GenerateEmail(int index, double errorRate, Random random)
    {
        // Introduce duplicate emails based on error rate
        if (random.NextDouble() < errorRate * 0.3) // 30% of errors are duplicate emails
        {
            return $"duplicate{random.Next(5)}@example.com"; // Create some duplicates
        }
        return $"member{index}@example.com";
    }

    private static string GenerateRandomDate(Random random, DateTime start, DateTime end)
    {
        TimeSpan timeSpan = end - start;
        var randomSpan = new TimeSpan((long)(random.NextDouble() * timeSpan.Ticks));
        return start.Add(randomSpan).ToString("yyyy-MM-dd");
    }

    private static string GeneratePlanId(Random random)
    {
        string[] plans = ["BASIC", "PREMIUM", "DELUXE"];
        return plans[random.Next(plans.Length)];
    }

    private static string GenerateHKID(int index, double errorRate, Random random)
    {
        // Introduce duplicate HKIDs based on error rate
        if (random.NextDouble() < errorRate * 0.2) // 20% of errors are duplicate HKIDs
        {
            return $"A123456({random.Next(3)})"; // Create some duplicates
        }
        return $"A{index:D6}(0)";
    }

    private static string GeneratePassport(int index, double errorRate, Random random)
    {
        // Introduce duplicate passports based on error rate  
        if (random.NextDouble() < errorRate * 0.1) // 10% of errors are duplicate passports
        {
            return $"P{random.Next(10):D7}"; // Create some duplicates
        }
        return $"P{index:D7}";
    }

    private static string GenerateRelationship(Random random)
    {
        string[] relationships = ["Employee", "Spouse", "Child"];
        return relationships[random.Next(relationships.Length)];
    }

    private static void IntroduceValidationErrors(Dictionary<string, string?> member, Random random)
    {
        // Randomly introduce various types of validation errors
        int errorType = random.Next(6);

        switch (errorType)
        {
            case 0: // Invalid email format
                member["email"] = "invalid-email";
                break;
            case 1: // Missing required field
                member["firstName"] = "";
                break;
            case 2: // Invalid date format
                member["dateOfBirth"] = "invalid-date";
                break;
            case 3: // Invalid plan ID
                member["planId"] = "INVALID_PLAN";
                break;
            case 4: // Invalid HKID format
                member["hkid"] = "INVALID_HKID";
                break;
            case 5: // Future date of birth
                member["dateOfBirth"] = DateTimeProvider.UtcNow.AddYears(1).ToString("yyyy-MM-dd");
                break;
        }
    }

    private List<Individual> CreateRealisticIndividuals(QueryArgumentsOfIndividualWhere args)
    {
        var individuals = new List<Individual>();

        if (args.Where?.InternalCode_in != null)
        {
            // Return 90% of requested individuals (simulate some missing)
            IEnumerable<string> memberIds = args.Where.InternalCode_in.Take((int)(args.Where.InternalCode_in.Count * 0.9));

            foreach (string memberId in memberIds)
            {
                individuals.Add(new Individual
                {
                    InternalCode = memberId,
                    EnglishFirstName = $"First_{memberId}",
                    EnglishLastName = $"Last_{memberId}"
                });
            }
        }

        return individuals;
    }

    private Dictionary<string, PolicyMember?> CreateRealisticPolicyMembers(List<string> memberIds)
    {
        var result = new Dictionary<string, PolicyMember?>();

        // Return existing policy members for 20% of the requests (simulate some existing members)
        foreach (string memberId in memberIds)
        {
            if (Random.Shared.NextDouble() < 0.2)
            {
                result[memberId] = CreateMockPolicyMember(memberId);
            }
            else
            {
                result[memberId] = null;
            }
        }

        return result;
    }

    private Dictionary<string, List<PolicyMember>> CreateRealisticValidationStates(List<string> memberIds)
    {
        var result = new Dictionary<string, List<PolicyMember>>();

        foreach (string memberId in memberIds)
        {
            // Some members have validation states, some don't
            if (Random.Shared.NextDouble() < 0.3)
            {
                result[memberId] = [CreateMockPolicyMember(memberId)];
            }
            else
            {
                result[memberId] = [];
            }
        }

        return result;
    }

    private static PolicyMember CreateMockPolicyMember(string memberId) =>
        // Create a minimal mock policy member with corrected constructor
        PolicyMember.Create(
            PolicyId.New,
            memberId,
            DateOnly.FromDateTime(DateTimeProvider.UtcNow.AddDays(-30)),
            DateOnly.FromDateTime(DateTimeProvider.UtcNow.AddYears(1)),
            "BASIC");

    private static PolicyMemberFieldsSchema CreateRealisticSchema()
    {
        var fields = new List<PolicyMemberFieldDefinition>
        {
            CreateFieldDefinition("memberId", true, "Member ID"),
            CreateFieldDefinition("email", true, "Email Address"),
            CreateFieldDefinition("firstName", true, "First Name"),
            CreateFieldDefinition("lastName", true, "Last Name"),
            CreateFieldDefinition("dateOfBirth", true, "Date of Birth"),
            CreateFieldDefinition("planId", true, "Plan ID"),
            CreateFieldDefinition("effectiveDate", true, "Effective Date"),
            CreateFieldDefinition("hkid", false, "Hong Kong ID"),
            CreateFieldDefinition("passport", false, "Passport Number"),
            CreateFieldDefinition("staffNumber", false, "Staff Number"),
            CreateFieldDefinition("relationship", true, "Relationship"),
            CreateFieldDefinition("gender", true, "Gender")
        };

        return new PolicyMemberFieldsSchema(fields);
    }

    private static PolicyMemberFieldDefinition CreateFieldDefinition(string name, bool required, string label) => new()
    {
        Name = name,
        Label = label,
        IsRequired = required,
        Type = name switch
        {
            "dateOfBirth" or "effectiveDate" => new DateFieldType(),
            "email" => new StringFieldType { Validations = @"^[^@\s]+@[^@\s]+\.[^@\s]+$" },
            _ => new StringFieldType()
        }
    };

    #endregion

    #region Test Helper Methods

    private async Task<PerformanceMetric> MeasureValidationPerformance(
        ValidatePolicyMemberUploadCommand command,
        string testName)
    {
        // Force garbage collection before measurement
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        // Measure performance
        long memoryBefore = GC.GetTotalMemory(forceFullCollection: true);
        var stopwatch = Stopwatch.StartNew();

        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        stopwatch.Stop();
        long memoryAfter = GC.GetTotalMemory(forceFullCollection: false);

        var metric = new PerformanceMetric
        {
            TestName = testName,
            ExecutionTime = stopwatch.Elapsed,
            MemoryUsed = memoryAfter - memoryBefore,
            IsSuccessful = result.IsSuccess,
            MemberCount = ExtractMemberCountFromCommand(command),
            ThroughputPerSecond = ExtractMemberCountFromCommand(command) / stopwatch.Elapsed.TotalSeconds
        };

        _performanceMetrics.Add(metric);
        return metric;
    }

    private static ValidatePolicyMemberUploadCommand CreateValidCommand() =>
        new() { PolicyId = PolicyId.New, UploadId = PolicyMemberUploadId.New };

    private static PolicyMemberUpload CreateUploadWithId(PolicyMemberUploadId id)
    {
        var upload = PolicyMemberUpload.Create(PolicyId.New, "test.xlsx", 1000);
        // Use reflection to set the ID to match the requested ID
        PropertyInfo? idProperty = typeof(PolicyMemberUpload).BaseType?.GetProperty("Id");
        if (idProperty != null && idProperty.CanWrite)
        {
            idProperty.SetValue(upload, id);
        }
        else
        {
            // Try to access the private field if property is not writable
            FieldInfo? idField = typeof(PolicyMemberUpload).BaseType?.GetField("_id",
                BindingFlags.NonPublic | BindingFlags.Instance);
            idField?.SetValue(upload, id);
        }
        return upload;
    }

    private static PolicyDto CreateValidPolicy() => new()
    {
        Id = Guid.NewGuid().ToString(),
        ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
        ContractHolderId = Guid.NewGuid().ToString(),
        StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
        IsIssued = false,
        Endorsements = new List<EndorsementDto>(),
        ApprovedEndorsementIds = new List<string>()
    };

    private static UploadWideValidationContext CreateUploadWideValidationContext(int memberCount)
    {
        var upload = PolicyMemberUpload.Create(PolicyId.New, "test.xlsx", memberCount);
        PolicyDto policy = CreateValidPolicy();
        MembersUploadFields memberData = CreateEmptyMembersUploadFields(memberCount);
        PolicyMemberFieldsSchema schema = CreateRealisticSchema();
        ResolvedValidationData resolvedData = CreateResolvedValidationData();

        return UploadWideValidationContext.Create(upload, policy, memberData, schema, resolvedData);
    }

    private static IndividualMemberValidationContext CreateIndividualMemberValidationContext(int memberCount)
    {
        var upload = PolicyMemberUpload.Create(PolicyId.New, "test.xlsx", memberCount);
        PolicyDto policy = CreateValidPolicy();
        MembersUploadFields memberData = CreateEmptyMembersUploadFields(memberCount);
        PolicyMemberFieldsSchema schema = CreateRealisticSchema();
        ResolvedValidationData resolvedData = CreateResolvedValidationData();

        return IndividualMemberValidationContext.Create(policy, memberData, schema, resolvedData, upload.EndorsementId);
    }

    private static MembersUploadFields CreateEmptyMembersUploadFields(int count)
    {
        var members = new List<MemberUploadFields>();
        for (int i = 0; i < count; i++)
        {
            var memberData = new Dictionary<string, string?>
            {
                ["memberId"] = $"M{i:D6}",
                ["email"] = $"member{i}@example.com",
                ["firstName"] = $"First{i}",
                ["lastName"] = $"Last{i}",
                ["planId"] = "BASIC"
            };
            members.Add(new MemberUploadFields(memberData));
        }
        return MembersUploadFields.CreateNonEmpty(members);
    }

    private static ResolvedValidationData CreateResolvedValidationData() => new()
    {
        UseTheSamePlanForEmployeeAndDependents = false,
        OnlyApplyForSmeProducts = false,
        AllowMembersFromOtherContractHolders = false,
        AvailablePlans = new HashSet<string> { "BASIC", "PREMIUM", "DELUXE" },
        IsProductSme = false,
        ContractHolderPolicyIds = new List<string>(),
        ValidEndorsementIds = new List<string>(),
        ContractHolderScopeEndorsements = new List<EndorsementId>(),
        IsPolicyV2 = false,
        TenantId = "test-tenant",
        DependentMembersCache = new Dictionary<string, PolicyMember?>(),
        ExistingIndividualIds = new HashSet<string>(),
        ExistingPolicyMembers = new Dictionary<string, PolicyMember?>(),
        MemberValidationStates = new Dictionary<string, IReadOnlyList<PolicyMember>>(),
        IndividualExistenceMap = new Dictionary<string, bool>()
    };

    private static int ExtractMemberCountFromCommand(ValidatePolicyMemberUploadCommand command) => 1000; // Default for testing

    public void Dispose()
    {
        // Output performance summary
        if (_performanceMetrics.Count > 0)
        {
            Console.WriteLine("\n=== REALISTIC Performance Test Summary ===");
            foreach (PerformanceMetric metric in _performanceMetrics)
            {
                Console.WriteLine($"{metric.TestName}: {metric.ExecutionTime.TotalMilliseconds:F2}ms, " +
                                $"{metric.MemoryUsed / 1024.0 / 1024.0:F2}MB, " +
                                $"{metric.ThroughputPerSecond:F1} members/sec, " +
                                $"Success: {metric.IsSuccessful}");
            }
            Console.WriteLine("================================================");
        }

        // Cleanup
        GC.Collect();
        GC.WaitForPendingFinalizers();
    }

    #endregion

    #region Performance Metric Class

    private class PerformanceMetric
    {
        public string TestName { get; set; } = string.Empty;
        public TimeSpan ExecutionTime { get; set; }
        public long MemoryUsed { get; set; }
        public bool IsSuccessful { get; set; }
        public int MemberCount { get; set; }
        public double ThroughputPerSecond { get; set; }
    }

    #endregion
}