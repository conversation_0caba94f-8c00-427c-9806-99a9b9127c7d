using System.Collections.Concurrent;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Tests.Unit.TestData;
using CoverGo.Users.Client;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.ValidateUpload;

/// <summary>
/// Tests for async architecture patterns in ValidatePolicyMemberUploadHandler.
/// Covers async pattern consistency, concurrent operations, exception propagation, 
/// cancellation coordination, resource management, and deadlock prevention.
/// </summary>
public class ValidatePolicyMemberUploadHandlerAsyncArchitectureTests
{
    #region Test Setup and Dependencies

    private readonly Mock<IPolicyMemberUploadRepository> _mockUploadRepository;
    private readonly Mock<IPolicyMemberUploadValidationErrorExporterService> _mockValidationErrorExporterService;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IFileProcessingService> _mockFileProcessingService;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<IPolicyMemberUniquenessService> _mockPolicyMemberUniquenessService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<ILogger<ValidatePolicyMemberUploadHandler>> _mockLogger;
    private readonly Mock<IMemoryCache> _mockMemoryCache;
    private readonly Mock<IUploadValidationOrchestrator> _mockValidationOrchestrator;

    private readonly CompleteUploadValidationSpecification _realCompleteValidationSpec;
    private readonly ValidatePolicyMemberUploadHandler _handler;
    private readonly TenantId _tenantId;
    private readonly Fixture _fixture;

    public ValidatePolicyMemberUploadHandlerAsyncArchitectureTests()
    {
        // Initialize mocks
        _mockUploadRepository = new Mock<IPolicyMemberUploadRepository>();
        _mockValidationErrorExporterService = new Mock<IPolicyMemberUploadValidationErrorExporterService>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockFileProcessingService = new Mock<IFileProcessingService>();
        _mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockPolicyMemberUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockProductService = new Mock<IProductService>();
        _mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        _mockUsersService = new Mock<IUsersService>();
        _mockLogger = new Mock<ILogger<ValidatePolicyMemberUploadHandler>>();
        _mockMemoryCache = new Mock<IMemoryCache>();
        _mockValidationOrchestrator = new Mock<IUploadValidationOrchestrator>();

        // Create real instances of specifications for integration testing
        var uploadUniqueEmailsSpec = new UploadMustHaveUniqueEmailsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueEmailsSpecification>>());
        var uploadUniqueIdSpec = new UploadMustHaveUniqueIdentificationSpecification(Mock.Of<ILogger<UploadMustHaveUniqueIdentificationSpecification>>());
        var uploadUniqueMemberIdsSpec = new UploadMustHaveUniqueMemberIdsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>());
        var dependentPlanSpec = new DependentAndEmployeeMustBeOnSamePlanSpecification(Mock.Of<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>());
        var uploadWideSpec = new UploadWideValidationSpecification(
            uploadUniqueEmailsSpec,
            uploadUniqueIdSpec,
            uploadUniqueMemberIdsSpec,
            dependentPlanSpec,
            _mockValidationOrchestrator.Object,
            Mock.Of<ILogger<UploadWideValidationSpecification>>());

        var memberUniqueEmailSpec = new MemberMustHaveUniqueEmailSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>());
        var memberUniqueHkidSpec = new MemberMustHaveUniqueHKIDSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>());
        var memberUniquePassportSpec = new MemberMustHaveUniquePassportSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>());
        var memberUniqueStaffSpec = new MemberMustHaveUniqueStaffNumberSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>());
        var memberIdBusinessRulesSpec = new MemberIdMustFollowBusinessRulesSpecification(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>());
        var memberFieldsSchemaSpec = new MemberFieldsMustMatchSchemaSpecification(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>());
        var memberEffectiveDateSpec = new MemberEffectiveDateMustBeValidSpecification(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>());
        var dependentValidationSpec = new DependentMustHaveValidPrimaryMemberSpecification(_mockPolicyMemberQueryService.Object, Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>());
        var memberValidPlanIdSpec = new MemberMustHaveValidPlanIdSpecification(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>());

        // Setup mock for IConcurrentMemberProcessor to return empty errors dictionary
        var mockConcurrentMemberProcessor = new Mock<IConcurrentMemberProcessor>();
        mockConcurrentMemberProcessor.Setup(x => x.ProcessMembersAsync(
                It.IsAny<int>(),
                It.IsAny<Func<int, Task<List<ValidationError>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var individualMemberSpec = new IndividualMemberValidationSpecification(
            memberUniqueEmailSpec,
            memberUniqueHkidSpec,
            memberUniquePassportSpec,
            memberUniqueStaffSpec,
            memberIdBusinessRulesSpec,
            memberFieldsSchemaSpec,
            memberEffectiveDateSpec,
            dependentValidationSpec,
            memberValidPlanIdSpec,
            mockConcurrentMemberProcessor.Object,
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());

        // Setup mock for IValidationErrorAggregator to return a valid result
        var mockValidationErrorAggregator = new Mock<IValidationErrorAggregator>();
        mockValidationErrorAggregator.Setup(x => x.AggregateResults(
                It.IsAny<List<BatchValidationResult>>(),
                It.IsAny<int>()))
            .Returns(new BatchValidationResult
            {
                ValidCount = 0,
                InvalidCount = 0,
                RowErrors = []
            });

        // Create real CompleteUploadValidationSpecification for integration testing
        _realCompleteValidationSpec = new CompleteUploadValidationSpecification(
            uploadWideSpec,
            individualMemberSpec,
            mockValidationErrorAggregator.Object,
            Mock.Of<ILogger<CompleteUploadValidationSpecification>>());

        _tenantId = new TenantId(Guid.NewGuid().ToString());
        _fixture = new Fixture();

        // Setup validation orchestrator to return empty errors by default
        _mockValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Create mock for PolicyMemberValidationDataService
        var mockValidationDataService = new Mock<PolicyMemberValidationDataService>(
            _mockLegacyPolicyService.Object,
            _mockProductService.Object,
            _mockFeatureManager.Object,
            _tenantId,
            _mockSchemaProvider.Object,
            new Mock<ILogger<PolicyMemberValidationDataService>>().Object);

        // Create handler with real specifications
        _handler = new ValidatePolicyMemberUploadHandler(
            _mockUploadRepository.Object,
            _mockValidationErrorExporterService.Object,
            _mockLegacyPolicyService.Object,
            _realCompleteValidationSpec,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            _mockFileProcessingService.Object,
            _mockLogger.Object,
            mockValidationDataService.Object);

        SetupDefaultMocks();
    }

    #endregion

    #region Async Pattern Consistency Tests

    [Fact]
    public async Task Handle_WithAsyncPatternConsistency_ShouldUseProperAwaitPatterns()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup async pattern verification - track all async calls
        var asyncCallTracker = new ConcurrentBag<string>();

        SetupAsyncCallTracking(asyncCallTracker);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("async patterns should execute correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify all async calls were properly awaited (no sync-over-async)
        asyncCallTracker.Should().NotBeEmpty("async calls should have been tracked");
        asyncCallTracker.Should().Contain(call => call.Contains("FindByIdAsync"), "repository calls should be async");
        asyncCallTracker.Should().Contain(call => call.Contains("GetPolicyById"), "policy service calls should be async");
        asyncCallTracker.Should().Contain(call => call.Contains("ProcessUploadFileAsync"), "file processing should be async");
    }

    // Task.WhenAll pattern test moved to PolicyMemberValidationDataServiceTests

    [Fact]
    public async Task Handle_WithConfigureAwaitFalse_ShouldNotCaptureContext()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup context capture verification
        var contextTracker = new ConcurrentBag<int>();

        SetupContextCaptureTracking(contextTracker);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("context-aware async execution should complete successfully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify context was not unnecessarily captured (performance optimization)
        // This is more of a code review item, but we can verify the pattern works correctly
    }

    #endregion

    #region Concurrent Operation Handling Tests

    [Fact]
    public async Task Handle_WithMultipleConcurrentValidations_ShouldHandleRaceConditions()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup concurrent execution with race condition simulation
        var concurrentCallCounter = new ConcurrentDictionary<string, int>();
        SetupConcurrentRaceConditionSimulation(concurrentCallCounter);

        // Act - Execute multiple concurrent validations
        Task<Result<ValidatePolicyMemberUploadResponse>>[] concurrentTasks = Enumerable.Range(0, 5)
            .Select(_ =>
            {
                // Create a unique command for each concurrent task to avoid upload locking conflicts
                var uniqueCommand = new ValidatePolicyMemberUploadCommand
                {
                    PolicyId = command.PolicyId,
                    UploadId = PolicyMemberUploadId.New
                };
                return _handler.Handle(uniqueCommand, CancellationToken.None);
            })
            .ToArray();

        Result<ValidatePolicyMemberUploadResponse>[] results = await Task.WhenAll(concurrentTasks);

        // Assert
        results.Should().AllSatisfy(result =>
            result.IsSuccess.Should().BeTrue("concurrent validations should handle race conditions gracefully"));

        // Verify repository locking mechanisms were used
        _mockUploadRepository.Verify(x => x.StartValidationIfNotLockedAsync(
                It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()),
            Times.AtLeast(5), "locking should be attempted for each concurrent operation");

        // Verify thread safety
        concurrentCallCounter.Values.Should().AllSatisfy(count =>
            count.Should().BeGreaterThan(0, "concurrent operations should have been tracked"));
    }

    // Concurrent service calls test moved to PolicyMemberValidationDataServiceTests

    [Fact]
    public async Task Handle_WithConcurrentResourceAccess_ShouldPreventResourceContention()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup resource contention simulation
        var resourceAccessTracker = new ConcurrentQueue<(string Resource, DateTime AccessTime, TimeSpan Duration)>();
        SetupResourceContentionSimulation(resourceAccessTracker);

        // Act - Execute with simulated resource contention
        Task<Result<ValidatePolicyMemberUploadResponse>>[] concurrentTasks = Enumerable.Range(0, 3)
            .Select(async _ =>
            {
                await Task.CompletedTask; // Remove staggered timing for deterministic tests
                // Create a unique command for each concurrent task to avoid upload locking conflicts
                var uniqueCommand = new ValidatePolicyMemberUploadCommand
                {
                    PolicyId = command.PolicyId,
                    UploadId = PolicyMemberUploadId.New
                };
                return await _handler.Handle(uniqueCommand, CancellationToken.None);
            })
            .ToArray();

        Result<ValidatePolicyMemberUploadResponse>[] results = await Task.WhenAll(concurrentTasks);

        // Assert
        results.Should().AllSatisfy(result =>
            result.IsSuccess.Should().BeTrue("resource contention should be handled gracefully"));

        // Verify resource access was properly managed
        resourceAccessTracker.Should().NotBeEmpty("resource access should have been tracked");

        // Verify no resource deadlocks occurred
        var accessTimes = resourceAccessTracker.Select(access => access.Duration).ToList();
        accessTimes.Should().AllSatisfy(duration =>
            duration.Should().BeLessThan(TimeSpan.FromSeconds(5), "resource access should not cause deadlocks"));
    }

    [Fact]
    public async Task Handle_WithHighConcurrencyLoad_ShouldMaintainPerformance()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup high concurrency performance tracking
        var performanceTracker = new ConcurrentBag<(DateTime StartTime, DateTime EndTime, bool Success)>();

        // Act - Execute high concurrency load (20 concurrent operations)
        Task<Result<ValidatePolicyMemberUploadResponse>>[] highConcurrencyTasks = Enumerable.Range(0, 20)
            .Select(async _ =>
            {
                DateTime startTime = DateTime.UtcNow;
                try
                {
                    // Create a unique command for each concurrent task to avoid upload locking conflicts
                    var uniqueCommand = new ValidatePolicyMemberUploadCommand
                    {
                        PolicyId = command.PolicyId,
                        UploadId = PolicyMemberUploadId.New
                    };
                    Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(uniqueCommand, CancellationToken.None);
                    DateTime endTime = DateTime.UtcNow;
                    performanceTracker.Add((startTime, endTime, result.IsSuccess));
                    return result;
                }
                catch
                {
                    DateTime endTime = DateTime.UtcNow;
                    performanceTracker.Add((startTime, endTime, false));
                    throw;
                }
            })
            .ToArray();

        Result<ValidatePolicyMemberUploadResponse>[] results = await Task.WhenAll(highConcurrencyTasks);

        // Assert
        results.Should().AllSatisfy(result =>
            result.IsSuccess.Should().BeTrue("high concurrency should not degrade functionality"));

        // Verify performance under high concurrency
        var performanceData = performanceTracker.ToList();
        performanceData.Should().HaveCount(20, "all concurrent operations should have been tracked");

        double averageExecutionTime = performanceData
            .Select(p => (p.EndTime - p.StartTime).TotalMilliseconds)
            .Average();

        averageExecutionTime.Should().BeLessThan(1000, "average execution time should remain reasonable under high concurrency");

        // Verify success rate under high concurrency
        double successRate = performanceData.Count(p => p.Success) / (double)performanceData.Count;
        successRate.Should().BeGreaterThan(0.95, "success rate should remain high under concurrent load");
    }

    #endregion

    #region Async Exception Propagation Tests

    [Fact]
    public async Task Handle_WithAsyncExceptionInService_ShouldPropagateCorrectly()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUpload();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        // Setup async exception in policy service
        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Async service exception"));

        // Act & Assert
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _handler.Handle(command, CancellationToken.None));

        exception.Message.Should().Be("Async service exception", "async exceptions should propagate correctly");
    }

    #endregion

    #region Task Cancellation Coordination Tests

    // Cancellation token propagation test moved to PolicyMemberValidationDataServiceTests

    [Fact]
    public async Task Handle_WithComplexCancellationScenario_ShouldCoordinateGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateLargeMemberDataset(5000);

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup complex cancellation scenario with multiple async operations
        var cancellationCoordinationTracker = new ConcurrentDictionary<string, (DateTime StartTime, DateTime? CancelTime)>();
        SetupComplexCancellationCoordination(cancellationCoordinationTracker);

        // Use pre-cancelled token for deterministic behavior
        using var cts = new CancellationTokenSource();
        cts.Cancel(); // Cancel immediately

        // Act & Assert - TaskCanceledException is a subclass of OperationCanceledException
        await Assert.ThrowsAsync<TaskCanceledException>(() =>
            _handler.Handle(command, cts.Token));

        // Verify coordinated cancellation across multiple operations
        cancellationCoordinationTracker.Should().NotBeEmpty("cancellation coordination should have been tracked");

        var cancelledOperations = cancellationCoordinationTracker.Values
            .Where(op => op.CancelTime.HasValue)
            .ToList();

        cancelledOperations.Should().NotBeEmpty("some operations should have been cancelled");
    }

    [Fact]
    public async Task Handle_WithCancellationDuringTaskWhenAll_ShouldCancelAllTasks()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup Task.WhenAll cancellation tracking - this must come AFTER SetupValidationScenario
        // to override the default feature manager setups
        var taskCancellationTracker = new ConcurrentBag<(string TaskName, bool WasCancelled)>();
        SetupTaskWhenAllCancellationTracking(taskCancellationTracker);

        // Create cancellation token that cancels during Task.WhenAll execution
        using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(100));

        // Act & Assert
        await Assert.ThrowsAsync<TaskCanceledException>(() =>
            _handler.Handle(command, cts.Token));

        // Verify all tasks in Task.WhenAll were cancelled
        taskCancellationTracker.Should().NotBeEmpty("task cancellation should have been tracked");
    }

    #endregion

    #region Async Resource Management Tests

    [Fact]
    public async Task Handle_WithAsyncResourceDisposal_ShouldDisposeCorrectly()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup resource disposal tracking
        var disposalTracker = new ConcurrentBag<(string Resource, DateTime DisposalTime)>();
        SetupResourceDisposalTracking(disposalTracker);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("async resource management should work correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify resources were properly disposed
        // Note: This is more about ensuring no resource leaks occur
        // The actual disposal tracking would depend on the specific resources used
    }

    [Fact]
    public async Task Handle_WithAsyncResourceLeakPrevention_ShouldNotLeakResources()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup resource leak detection
        var resourceTracker = new ConcurrentDictionary<string, int>();
        SetupResourceLeakDetection(resourceTracker);

        // Act - Execute multiple times to detect potential leaks
        for (int i = 0; i < 10; i++)
        {
            // Create a unique command for each iteration to avoid upload locking conflicts
            var uniqueCommand = new ValidatePolicyMemberUploadCommand
            {
                PolicyId = command.PolicyId,
                UploadId = PolicyMemberUploadId.New
            };
            Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(uniqueCommand, CancellationToken.None);
            result.IsSuccess.Should().BeTrue($"iteration {i} should succeed");
        }

        // Force garbage collection to detect leaks
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        // Assert - Verify no resource accumulation
        resourceTracker.Values.Should().AllSatisfy(count =>
            count.Should().BeLessThan(50, "resource usage should not accumulate"));
    }

    #endregion

    #region Deadlock Prevention Tests

    [Fact]
    public async Task Handle_WithDeadlockPreventionPattern_ShouldNotDeadlock()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup deadlock detection
        var deadlockDetector = new ConcurrentBag<(DateTime StartTime, DateTime EndTime, int ThreadId)>();
        SetupDeadlockDetection(deadlockDetector);

        // Act - Execute multiple concurrent operations that could potentially deadlock
        Task<Result<ValidatePolicyMemberUploadResponse>>[] deadlockTestTasks = Enumerable.Range(0, 10)
            .Select(async _ =>
            {
                DateTime startTime = DateTime.UtcNow;
                int threadId = Thread.CurrentThread.ManagedThreadId;

                // Create a unique command for each concurrent task to avoid upload locking conflicts
                var uniqueCommand = new ValidatePolicyMemberUploadCommand
                {
                    PolicyId = command.PolicyId,
                    UploadId = PolicyMemberUploadId.New
                };
                Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(uniqueCommand, CancellationToken.None);

                DateTime endTime = DateTime.UtcNow;
                deadlockDetector.Add((startTime, endTime, threadId));

                return result;
            })
            .ToArray();

        // Set a reasonable timeout to detect deadlocks
        using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(10));

        Result<ValidatePolicyMemberUploadResponse>[] results = await Task.WhenAll(deadlockTestTasks)
            .WaitAsync(timeoutCts.Token);

        // Assert
        results.Should().AllSatisfy(result =>
            result.IsSuccess.Should().BeTrue("operations should complete without deadlocks"));

        // Verify no deadlocks occurred (all operations completed in reasonable time)
        var executionTimes = deadlockDetector.Select(d => (d.EndTime - d.StartTime).TotalMilliseconds).ToList();
        executionTimes.Should().AllSatisfy(time =>
            time.Should().BeLessThan(5000, "no operation should take longer than 5 seconds (indicating potential deadlock)"));
    }

    #endregion

    #region Helper Methods

    private ValidatePolicyMemberUploadCommand CreateValidCommand() => new()
    {
        PolicyId = Guid.NewGuid(),
        UploadId = Guid.NewGuid()
    };

    private PolicyMemberUpload CreateValidUpload() => MemberUploadTestDataBuilder.Create().BuildPolicyMemberUpload();

    private PolicyDto CreateValidPolicy() => MemberUploadTestDataBuilder.Create().BuildPolicyDto();

    private PolicyMemberFieldsSchema CreateTestSchema() => MemberUploadTestDataBuilder.Create().BuildSchema();

    private List<IReadOnlyDictionary<string, string?>> CreateValidMemberData()
    {
        var memberData = new Dictionary<string, string?>
        {
            { "Plan ID", "PLAN-001" },
            { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
            { "First Name", "John" },
            { "Last Name", "Doe" },
            { "Name", "John Doe" },
            { "Member Type", "employee" },
            { "Date of Birth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "Email", "<EMAIL>" },
            { "HKID", "A123456(7)" },
            { "Staff Number", "STAFF001" },
            { "Member ID", "STAFF001" } // Add Member ID field using field labels
        };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateLargeMemberDataset(int count)
    {
        List<IReadOnlyDictionary<string, string?>> dataset = [];

        for (int i = 1; i <= count; i++)
        {
            var member = new Dictionary<string, string?>
            {
                { "Plan ID", $"PLAN-{i % 3 + 1:D3}" },
                { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
                { "First Name", $"AsyncTest{i}" },
                { "Last Name", $"Member{i}" },
                { "Name", $"AsyncTest{i} Member{i}" },
                { "Member Type", "employee" },
                { "Date of Birth", DateTime.Today.AddYears(-30 - (i % 20)).ToString("yyyy-MM-dd") },
                { "Email", $"async.test{i}@test.com" },
                { "HKID", $"A{i:D6}({(i % 10)})" },
                { "Staff Number", $"ASYNC{i:D6}" },
                { "Member ID", $"ASYNC{i:D6}" } // Use field labels instead of field names
            };

            dataset.Add(member);
        }

        return dataset;
    }

    private void SetupValidationScenario(
        IReadOnlyList<IReadOnlyDictionary<string, string?>> memberData,
        PolicyDto? policy = null,
        PolicyMemberFieldsSchema? schema = null,
        ResolvedValidationData? resolvedData = null)
    {
        PolicyDto testPolicy = policy ?? CreateValidPolicy();
        PolicyMemberFieldsSchema testSchema = schema ?? CreateTestSchema();
        _ = resolvedData ?? ResolvedValidationDataTestDataBuilder.Create();

        // Return a unique upload instance for each upload ID to avoid state conflicts in concurrent tests
        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMemberUploadId _, CancellationToken _) => CreateValidUpload());

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testPolicy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testSchema);

        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        // If your validation context is constructed via a service or factory, mock it here to return the context with testResolvedData.
        // (If not, ensure downstream code can access testResolvedData as required.)

        SetupDefaultMocks();
    }

    private void SetupAsyncCallTracking(ConcurrentBag<string> asyncCallTracker)
    {
        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .Returns<PolicyMemberUploadId, CancellationToken>(async (_, _) =>
            {
                asyncCallTracker.Add($"FindByIdAsync-{DateTime.UtcNow:HH:mm:ss.fff}");
                await Task.CompletedTask;
                return CreateValidUpload();
            });

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, CancellationToken>(async (_, _) =>
            {
                asyncCallTracker.Add($"GetPolicyById-{DateTime.UtcNow:HH:mm:ss.fff}");
                await Task.CompletedTask;
                return CreateValidPolicy();
            });

        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, string, CancellationToken>(async (_, _, _) =>
            {
                asyncCallTracker.Add($"ProcessUploadFileAsync-{DateTime.UtcNow:HH:mm:ss.fff}");
                await Task.CompletedTask;
                return FileProcessingResult.Success(CreateValidMemberData(), 1);
            });
    }

    private void SetupConcurrentExecutionTracking(
        ConcurrentDictionary<string, DateTime> concurrentExecutionTracker,
        ConcurrentBag<(string Operation, DateTime StartTime, DateTime EndTime)> executionOrder)
    {
        // Track GetAvailablePlanIds (runs concurrently in GatherValidationDataAsync)
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .Returns<ProductId, CancellationToken>(async (_, ct) =>
            {
                DateTime startTime = DateTime.UtcNow;
                concurrentExecutionTracker.TryAdd($"GetAvailablePlanIds-{startTime:HH:mm:ss.fff}", startTime);
                await Task.Delay(50, ct); // Simulate work
                DateTime endTime = DateTime.UtcNow;
                executionOrder.Add(("GetAvailablePlanIds", startTime, endTime));
                return ["PLAN-001", "PLAN-002"];
            });

        // Track GetProductPackageType (runs concurrently in GatherValidationDataAsync)
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .Returns<Products.Client.ProductId, CancellationToken>(async (_, ct) =>
            {
                DateTime startTime = DateTime.UtcNow;
                concurrentExecutionTracker.TryAdd($"GetProductPackageType-{startTime:HH:mm:ss.fff}", startTime);
                await Task.Delay(50, ct); // Simulate work
                DateTime endTime = DateTime.UtcNow;
                executionOrder.Add(("GetProductPackageType", startTime, endTime));
                return "sme";
            });

        // Track GetIdsByContractHolderId (runs concurrently in GatherValidationDataAsync)
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, CancellationToken>(async (_, ct) =>
            {
                DateTime startTime = DateTime.UtcNow;
                concurrentExecutionTracker.TryAdd($"GetIdsByContractHolderId-{startTime:HH:mm:ss.fff}", startTime);
                await Task.Delay(50, ct); // Simulate work
                DateTime endTime = DateTime.UtcNow;
                executionOrder.Add(("GetIdsByContractHolderId", startTime, endTime));
                return ["POL-001", "POL-002"];
            });

        // Track feature flag checks (run concurrently in GatherValidationDataAsync)
        _mockFeatureManager.Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .Returns<string, string>(async (featureName, _) =>
            {
                DateTime startTime = DateTime.UtcNow;
                concurrentExecutionTracker.TryAdd($"FeatureCheck-{featureName}-{startTime:HH:mm:ss.fff}", startTime);
                await Task.Delay(30); // Simulate work
                DateTime endTime = DateTime.UtcNow;
                executionOrder.Add(($"FeatureCheck-{featureName}", startTime, endTime));
                return false;
            });
    }

    private void SetupContextCaptureTracking(ConcurrentBag<int> contextTracker) =>
        // This would typically involve checking SynchronizationContext.Current
        // For this test, we'll just verify the pattern works
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .Returns<Products.Client.ProductId, CancellationToken>(async (_, _) =>
            {
                contextTracker.Add(Thread.CurrentThread.ManagedThreadId);
                await Task.CompletedTask;
                return "sme";
            });

    private void VerifyConcurrentExecution(List<(string Operation, DateTime StartTime, DateTime EndTime)> orderedExecutions)
    {
        if (orderedExecutions.Count < 2) return;

        // Check if any operations overlapped in time (indicating concurrent execution)
        bool foundConcurrentExecution = false;
        for (int i = 0; i < orderedExecutions.Count - 1; i++)
        {
            for (int j = i + 1; j < orderedExecutions.Count; j++)
            {
                (string Operation, DateTime StartTime, DateTime EndTime) op1 = orderedExecutions[i];
                (string Operation, DateTime StartTime, DateTime EndTime) op2 = orderedExecutions[j];

                // Check if operations overlapped
                if (op1.StartTime < op2.EndTime && op2.StartTime < op1.EndTime)
                {
                    foundConcurrentExecution = true;
                    break;
                }
            }
            if (foundConcurrentExecution) break;
        }

        foundConcurrentExecution.Should().BeTrue("operations should have executed concurrently");
    }

    private void SetupConcurrentRaceConditionSimulation(ConcurrentDictionary<string, int> concurrentCallCounter) => _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
        .Returns<PolicyMemberUploadId, CancellationToken>(async (_, ct) =>
        {
            concurrentCallCounter.AddOrUpdate("StartValidation", 1, (_, value) => value + 1);
            await Task.Delay(10, ct); // Simulate work
            return true;
        });

    private void SetupConcurrentServiceCallTracking(ConcurrentBag<(string Service, DateTime CallTime, int ThreadId)> serviceCallTracker)
    {
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .Returns<ProductId, CancellationToken>(async (_, ct) =>
            {
                serviceCallTracker.Add(("ProductService", DateTime.UtcNow, Thread.CurrentThread.ManagedThreadId));
                await Task.Delay(20, ct);
                return ["PLAN-001"];
            });

        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .Returns<QueryArgumentsOfIndividualWhere, CancellationToken>(async (_, ct) =>
            {
                serviceCallTracker.Add(("UsersService", DateTime.UtcNow, Thread.CurrentThread.ManagedThreadId));
                await Task.Delay(20, ct);
                return [];
            });

        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .Returns<List<string>, PolicyId, List<EndorsementId>, CancellationToken>(async (_, _, _, ct) =>
            {
                serviceCallTracker.Add(("PolicyMemberQueryService", DateTime.UtcNow, Thread.CurrentThread.ManagedThreadId));
                await Task.Delay(20, ct);
                return [];
            });

        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, CancellationToken>(async (_, ct) =>
            {
                serviceCallTracker.Add(("LegacyPolicyService", DateTime.UtcNow, Thread.CurrentThread.ManagedThreadId));
                await Task.Delay(20, ct);
                return [];
            });
    }

    private void SetupResourceContentionSimulation(ConcurrentQueue<(string Resource, DateTime AccessTime, TimeSpan Duration)> resourceAccessTracker) => _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
        .Returns<string, string, CancellationToken>(async (_, _, ct) =>
        {
            DateTime accessTime = DateTime.UtcNow;
            await Task.Delay(100, ct); // Simulate resource access time
            TimeSpan duration = DateTime.UtcNow - accessTime;
            resourceAccessTracker.Enqueue(("FileProcessing", accessTime, duration));
            return FileProcessingResult.Success(CreateValidMemberData(), 1);
        });

    private void SetupCancellationTracking(ConcurrentBag<string> cancellationTracker) => _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
        .Returns<string, CancellationToken>(async (_, ct) =>
        {
            try
            {
                await Task.Delay(1000, ct); // Long operation that should be cancelled
                return CreateValidPolicy();
            }
            catch (OperationCanceledException)
            {
                cancellationTracker.Add($"GetPolicyById-Cancelled-{DateTime.UtcNow:HH:mm:ss.fff}");
                throw;
            }
        });

    private void SetupComplexCancellationCoordination(ConcurrentDictionary<string, (DateTime StartTime, DateTime? CancelTime)> cancellationCoordinationTracker)
    {
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .Returns<ProductId, CancellationToken>(async (_, ct) =>
            {
                DateTime startTime = DateTime.UtcNow;
                cancellationCoordinationTracker.TryAdd("GetAvailablePlanIds", (startTime, null));
                try
                {
                    await Task.Delay(2000, ct);
                    return ["PLAN-001"];
                }
                catch (OperationCanceledException)
                {
                    cancellationCoordinationTracker.TryUpdate("GetAvailablePlanIds", (startTime, DateTime.UtcNow), (startTime, null));
                    throw;
                }
            });

        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .Returns<QueryArgumentsOfIndividualWhere, CancellationToken>(async (_, ct) =>
            {
                DateTime startTime = DateTime.UtcNow;
                cancellationCoordinationTracker.TryAdd("QueryIndividuals", (startTime, null));
                try
                {
                    await Task.Delay(2000, ct);
                    return [];
                }
                catch (OperationCanceledException)
                {
                    cancellationCoordinationTracker.TryUpdate("QueryIndividuals", (startTime, DateTime.UtcNow), (startTime, null));
                    throw;
                }
            });
    }

    private void SetupTaskWhenAllCancellationTracking(ConcurrentBag<(string TaskName, bool WasCancelled)> taskCancellationTracker) =>
        // Override the file processing service to have a delay that can be cancelled
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, string, CancellationToken>(async (_, _, ct) =>
            {
                try
                {
                    await Task.Delay(500, ct); // Long enough to be cancelled
                    taskCancellationTracker.Add(("ProcessUploadFileAsync", false));
                    return FileProcessingResult.Success(CreateValidMemberData(), 1);
                }
                catch (OperationCanceledException)
                {
                    taskCancellationTracker.Add(("ProcessUploadFileAsync", true));
                    throw;
                }
            });

    private void SetupResourceDisposalTracking(ConcurrentBag<(string Resource, DateTime DisposalTime)> disposalTracker)
    {
        // This would typically track IDisposable resources
        // For this test, we'll simulate resource tracking
    }

    private void SetupResourceLeakDetection(ConcurrentDictionary<string, int> resourceTracker) =>
        // Simulate resource usage tracking
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, string, CancellationToken>(async (_, _, ct) =>
            {
                resourceTracker.AddOrUpdate("FileProcessing", 1, (_, value) => value + 1);
                await Task.Delay(10, ct);
                return FileProcessingResult.Success(CreateValidMemberData(), 1);
            });

    private void SetupDeadlockDetection(ConcurrentBag<(DateTime StartTime, DateTime EndTime, int ThreadId)> deadlockDetector) =>
        // Setup services with potential for deadlock scenarios
        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, CancellationToken>(async (_, ct) =>
            {
                await Task.Delay(100, ct); // Simulate work without deadlock
                return CreateValidPolicy();
            });

    private void SetupDefaultMocks()
    {
        // Setup memory cache mock
        var mockCacheEntry = new Mock<ICacheEntry>();
        _mockMemoryCache.Setup(x => x.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);
        _mockMemoryCache.Setup(x => x.TryGetValue(It.IsAny<object>(), out It.Ref<object?>.IsAny)).Returns(false);

        // Setup default feature flags
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup default product service responses
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");

        // Setup default policy service responses
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyDtosByIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default users service responses
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default member query service responses
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default uniqueness service responses (no conflicts)
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(), It.IsAny<List<PolicyId>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup repository operations
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
                It.IsAny<PolicyMemberUploadId>(), It.IsAny<PolicyMemberUploadStatus>(),
                It.IsAny<int>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // CRITICAL: Setup validation orchestrator to return successful validation
        _mockValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
    }

    #endregion
}