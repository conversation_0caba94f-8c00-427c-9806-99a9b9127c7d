using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Tests.Unit.TestData;
using CoverGo.Users.Client;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.ValidateUpload;

/// <summary>
/// Tests for individual field validation in ValidatePolicyMemberUploadHandler.
/// Covers Plan ID validation, Dependent relationship validation, and Member ID validation scenarios.
/// </summary>
public class ValidatePolicyMemberUploadHandlerFieldValidationTests
{
    #region Test Setup and Dependencies

    private readonly Mock<IPolicyMemberUploadRepository> _mockUploadRepository;
    private readonly Mock<IPolicyMemberUploadValidationErrorExporterService> _mockValidationErrorExporterService;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IFileProcessingService> _mockFileProcessingService;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<IPolicyMemberUniquenessService> _mockPolicyMemberUniquenessService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<ILogger<ValidatePolicyMemberUploadHandler>> _mockLogger;
    private readonly Mock<IMemoryCache> _mockMemoryCache;
    private readonly Mock<ResolvedValidationData> _mockResolvedValidationData;

    private readonly CompleteUploadValidationSpecification _realCompleteValidationSpec;
    private readonly ValidatePolicyMemberUploadHandler _handler;
    private readonly TenantId _tenantId;
    private readonly Fixture _fixture;

    public ValidatePolicyMemberUploadHandlerFieldValidationTests()
    {
        // Initialize mocks
        _mockUploadRepository = new Mock<IPolicyMemberUploadRepository>();
        _mockValidationErrorExporterService = new Mock<IPolicyMemberUploadValidationErrorExporterService>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockFileProcessingService = new Mock<IFileProcessingService>();
        _mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockPolicyMemberUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockProductService = new Mock<IProductService>();
        _mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        _mockUsersService = new Mock<IUsersService>();
        _mockLogger = new Mock<ILogger<ValidatePolicyMemberUploadHandler>>();
        _mockMemoryCache = new Mock<IMemoryCache>();
        _mockResolvedValidationData = new Mock<ResolvedValidationData>();

        // Create real instances of specifications for integration testing
        var uploadUniqueEmailsSpec = new UploadMustHaveUniqueEmailsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueEmailsSpecification>>());
        var uploadUniqueIdSpec = new UploadMustHaveUniqueIdentificationSpecification(Mock.Of<ILogger<UploadMustHaveUniqueIdentificationSpecification>>());
        var uploadUniqueMemberIdsSpec = new UploadMustHaveUniqueMemberIdsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>());
        var dependentPlanSpec = new DependentAndEmployeeMustBeOnSamePlanSpecification(Mock.Of<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>());

        // Setup mock for IUploadValidationOrchestrator to return empty errors dictionary
        var mockUploadValidationOrchestrator = new Mock<IUploadValidationOrchestrator>();
        mockUploadValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var uploadWideSpec = new UploadWideValidationSpecification(
            uploadUniqueEmailsSpec,
            uploadUniqueIdSpec,
            uploadUniqueMemberIdsSpec,
            dependentPlanSpec,
            mockUploadValidationOrchestrator.Object,
            Mock.Of<ILogger<UploadWideValidationSpecification>>());

        var memberUniqueEmailSpec = new MemberMustHaveUniqueEmailSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>());
        var memberUniqueHkidSpec = new MemberMustHaveUniqueHKIDSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>());
        var memberUniquePassportSpec = new MemberMustHaveUniquePassportSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>());
        var memberUniqueStaffSpec = new MemberMustHaveUniqueStaffNumberSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>());
        var memberIdBusinessRulesSpec = new MemberIdMustFollowBusinessRulesSpecification(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>());
        var memberFieldsSchemaSpec = new MemberFieldsMustMatchSchemaSpecification(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>());
        var memberEffectiveDateSpec = new MemberEffectiveDateMustBeValidSpecification(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>());
        var dependentValidationSpec = new DependentMustHaveValidPrimaryMemberSpecification(_mockPolicyMemberQueryService.Object, Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>());
        var memberValidPlanIdSpec = new MemberMustHaveValidPlanIdSpecification(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>());

        // Setup mock for IConcurrentMemberProcessor to return empty errors dictionary
        var mockConcurrentMemberProcessor = new Mock<IConcurrentMemberProcessor>();
        mockConcurrentMemberProcessor.Setup(x => x.ProcessMembersAsync(
                It.IsAny<int>(),
                It.IsAny<Func<int, Task<List<ValidationError>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var individualMemberSpec = new IndividualMemberValidationSpecification(
            memberUniqueEmailSpec,
            memberUniqueHkidSpec,
            memberUniquePassportSpec,
            memberUniqueStaffSpec,
            memberIdBusinessRulesSpec,
            memberFieldsSchemaSpec,
            memberEffectiveDateSpec,
            dependentValidationSpec,
            memberValidPlanIdSpec,
            mockConcurrentMemberProcessor.Object,
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());

        // Create real CompleteUploadValidationSpecification for integration testing
        var mockErrorAggregator = new Mock<IValidationErrorAggregator>();
        mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns((List<BatchValidationResult> results, int _) =>
            {
                int totalValid = results.Sum(r => r.ValidCount);
                int totalInvalid = results.Sum(r => r.InvalidCount);
                var allErrors = results.SelectMany(r => r.RowErrors).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                return BatchValidationResult.WithErrors(totalValid, totalInvalid, allErrors);
            });

        _realCompleteValidationSpec = new CompleteUploadValidationSpecification(
            uploadWideSpec,
            individualMemberSpec,
            mockErrorAggregator.Object,
            Mock.Of<ILogger<CompleteUploadValidationSpecification>>());

        _tenantId = new TenantId(Guid.NewGuid().ToString());
        _fixture = new Fixture();

        // Create mock for PolicyMemberValidationDataService
        var mockValidationDataService = new Mock<PolicyMemberValidationDataService>(
            _mockLegacyPolicyService.Object,
            _mockProductService.Object,
            _mockFeatureManager.Object,
            _tenantId,
            _mockSchemaProvider.Object,
            new Mock<ILogger<PolicyMemberValidationDataService>>().Object);

        // Create handler with real specifications
        _handler = new ValidatePolicyMemberUploadHandler(
            _mockUploadRepository.Object,
            _mockValidationErrorExporterService.Object,
            _mockLegacyPolicyService.Object,
            _realCompleteValidationSpec,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            _mockFileProcessingService.Object,
            _mockLogger.Object,
            mockValidationDataService.Object);

        SetupDefaultMocks();
    }

    #endregion

    #region Plan ID Validation Tests

    [Fact]
    public async Task Handle_WithValidPlanId_ShouldPassValidation()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with valid plan ID
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithPlanId("PLAN-001");

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup product service to return available plans including the test plan
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("validation should pass when plan ID exists in available plans");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithInvalidPlanId_ShouldReturnValidationError()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with invalid plan ID
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithPlanId("INVALID-PLAN");

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup product service to return available plans NOT including the test plan
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with validation errors");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // The validation errors should be captured in the upload's validation results
        // rather than causing the handler to fail
    }

    [Fact]
    public async Task Handle_WithNullPlanId_ShouldPassValidation()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with null plan ID (optional field)
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithPlanId(null);

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup product service to return available plans
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("validation should pass when plan ID is null (optional field)");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithMissingProductId_ShouldSkipPlanValidation()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreatePolicyWithoutProductId();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with any plan ID
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithPlanId("ANY-PLAN");

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("validation should pass when policy lacks product information");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify product service was not called for plan validation
        _mockProductService.Verify(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    #endregion

    #region Dependent Relationship Validation Tests

    [Fact]
    public async Task Handle_WithNonDependentMembers_ShouldPassValidation()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with regular employees/primary members
        List<IReadOnlyDictionary<string, string?>> memberData = CreateNonDependentMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("validation should pass for regular employees/primary members");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithValidDependentRelationships_ShouldPassValidation()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with valid dependent-to-primary member relationships
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidDependentRelationshipData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup policy member query service to return existing primary member
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMemberCurrentStateAsync(
                It.IsAny<string>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateMockPolicyMember());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("validation should pass for valid dependent relationships");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithInvalidDependentReferences_ShouldReturnValidationError()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with dependent referencing non-existent primary member
        List<IReadOnlyDictionary<string, string?>> memberData = CreateInvalidDependentRelationshipData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup policy member query service to return null (member not found)
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMemberCurrentStateAsync(
                It.IsAny<string>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMember?)null);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with validation errors");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // The validation errors should be captured in the upload's validation results
    }

    #endregion

    #region Member ID Validation Tests

    [Fact]
    public async Task Handle_WithNullMemberId_ShouldPassValidation()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with null member ID (for new members)
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberId(null);

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("validation should pass for new members with null member ID");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithValidMemberId_ShouldPassValidation()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with valid member ID
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberId("MEMBER-001");

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup users service to return existing individual
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([CreateMockIndividual("MEMBER-001")]);

        // Setup policy member query service to return no existing policy member (available for assignment)
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, PolicyMember?> { { "MEMBER-001", null } });

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("validation should pass for valid member ID");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithMemberNotFound_ShouldReturnValidationError()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with member ID that doesn't exist
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberId("NONEXISTENT-MEMBER");

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup users service to return no individuals (member not found)
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with validation errors");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // The validation errors should be captured in the upload's validation results
    }

    [Fact]
    public async Task Handle_WithMemberIdConflicts_ShouldReturnValidationError()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with member ID that's already taken
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberId("TAKEN-MEMBER");

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup users service to return existing individual
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([CreateMockIndividual("TAKEN-MEMBER")]);

        // Setup policy member query service to return existing policy member (already taken)
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, PolicyMember?> { { "TAKEN-MEMBER", CreateMockPolicyMember() } });

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with validation errors");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // The validation errors should be captured in the upload's validation results
    }

    #endregion

    #region Helper Methods

    private ValidatePolicyMemberUploadCommand CreateValidCommand() => new()
    {
        PolicyId = Guid.NewGuid(),
        UploadId = Guid.NewGuid()
    };

    private PolicyMemberUpload CreateValidUpload() => MemberUploadTestDataBuilder.Create().BuildPolicyMemberUpload();

    private PolicyDto CreateValidPolicy() => MemberUploadTestDataBuilder.Create().BuildPolicyDto();

    private PolicyDto CreatePolicyWithoutProductId() => new()
    {
        Id = Guid.NewGuid().ToString(),
        ProductId = null, // This is what we want to test
        ContractHolderId = Guid.NewGuid().ToString(),
        StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
        EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
        IsIssued = false,
        Endorsements = [],
        ApprovedEndorsementIds = []
    };

    private PolicyMemberFieldsSchema CreateTestSchema() => MemberUploadTestDataBuilder.Create().BuildSchema();

    private List<IReadOnlyDictionary<string, string?>> CreateMemberDataWithPlanId(string? planId)
    {
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "planId", planId },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "John" },
            { "lastName", "Doe" },
            { "name", "John Doe" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" },
            { "hkid", "A123456(7)" },
            { "staffNo", "STAFF001" }
        };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateNonDependentMemberData()
    {
        Dictionary<string, string?> employee1 = new Dictionary<string, string?>
        {
            { "planId", "PLAN-001" },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "John" },
            { "lastName", "Doe" },
            { "name", "John Doe" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" },
            { "hkid", "A123456(7)" },
            { "staffNo", "STAFF001" }
        };

        Dictionary<string, string?> employee2 = new Dictionary<string, string?>
        {
            { "planId", "PLAN-002" },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "Jane" },
            { "lastName", "Smith" },
            { "name", "Jane Smith" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-25).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" },
            { "hkid", "B123456(8)" },
            { "staffNo", "STAFF002" }
        };

        return [employee1, employee2];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateValidDependentRelationshipData()
    {
        // Primary member
        Dictionary<string, string?> primaryMember = new Dictionary<string, string?>
        {
            { "memberId", "PRIMARY-001" },
            { "planId", "PLAN-001" },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "John" },
            { "lastName", "Doe" },
            { "name", "John Doe" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" },
            { "hkid", "A123456(7)" },
            { "staffNo", "STAFF001" }
        };

        // Dependent member with valid reference
        Dictionary<string, string?> dependentMember = new Dictionary<string, string?>
        {
            { "planId", "PLAN-001" },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "Jane" },
            { "lastName", "Doe" },
            { "name", "Jane Doe" },
            { "memberType", "dependent" },
            { "dateOfBirth", DateTime.Today.AddYears(-25).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" },
            { "hkid", "B123456(8)" },
            { "dependentOf", "PRIMARY-001" } // Valid reference to existing primary member
        };

        return [primaryMember, dependentMember];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateInvalidDependentRelationshipData()
    {
        // Dependent member with invalid reference
        Dictionary<string, string?> dependentMember = new Dictionary<string, string?>
        {
            { "planId", "PLAN-001" },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "Jane" },
            { "lastName", "Doe" },
            { "name", "Jane Doe" },
            { "memberType", "dependent" },
            { "dateOfBirth", DateTime.Today.AddYears(-25).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" },
            { "hkid", "B123456(8)" },
            { "dependentOf", "NONEXISTENT-PRIMARY" } // Invalid reference to non-existent primary member
        };

        return [dependentMember];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateMemberDataWithMemberId(string? memberId)
    {
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "memberId", memberId },
            { "planId", "PLAN-001" },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "John" },
            { "lastName", "Doe" },
            { "name", "John Doe" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" },
            { "hkid", "A123456(7)" },
            { "staffNo", "STAFF001" }
        };

        return [memberData];
    }

    private Individual CreateMockIndividual(string internalCode) => new()
    {
        InternalCode = internalCode
    };

    private PolicyMember CreateMockPolicyMember() => PolicyMember.Create(
        PolicyId.New,
        "TEST-MEMBER",
        DateOnly.FromDateTime(DateTime.Today),
        null,
        "PLAN-001");

    private void SetupValidationScenario(
        List<IReadOnlyDictionary<string, string?>> memberData,
        PolicyDto? policy = null,
        PolicyMemberFieldsSchema? schema = null,
        ResolvedValidationData? resolvedValidationData = null)
    {
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto testPolicy = policy ?? CreateValidPolicy();
        PolicyMemberFieldsSchema testSchema = schema ?? CreateTestSchema();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testPolicy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testSchema);

        // Setup file processing service to return test member data
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        SetupDefaultMocks();
    }

    private void SetupDefaultMocks()
    {
        // Setup memory cache mock
        var mockCacheEntry = new Mock<ICacheEntry>();
        _mockMemoryCache.Setup(x => x.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);
        _mockMemoryCache.Setup(x => x.TryGetValue(It.IsAny<object>(), out It.Ref<object?>.IsAny)).Returns(false);

        // Setup default feature flags
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup default product service responses
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");

        // Setup default policy service responses
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default users service responses
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default member query service responses
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default uniqueness service responses
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup repository operations
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
                It.IsAny<PolicyMemberUploadId>(),
                It.IsAny<PolicyMemberUploadStatus>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
    }

    #endregion
}