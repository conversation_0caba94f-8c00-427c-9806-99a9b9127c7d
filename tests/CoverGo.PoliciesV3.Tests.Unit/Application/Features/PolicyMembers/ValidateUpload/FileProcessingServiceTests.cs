using System.Text;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Products;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Infrastructure.FileProcessing;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.ValidateUpload;

[Trait("Category", "Integration")]
[Trait("Component", "FileProcessing")]
[Trait("Feature", "BehavioralCompatibility")]
public class FileProcessingServiceTests
{
    private readonly Mock<IPolicyMemberUploadRepository> _mockUploadRepository;
    private readonly Mock<IPolicyMemberUploadValidationErrorExporterService> _mockValidationErrorExporterService;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IFileSystemService> _mockFileSystemService;
    private readonly Mock<IFileParserFactory> _mockFileParserFactory;
    private readonly Mock<IFileParser> _mockFileParser;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<CompleteUploadValidationSpecification> _mockCompleteValidationSpec;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<ILogger<ValidatePolicyMemberUploadHandler>> _mockLogger;
    private readonly Mock<ILogger<FileProcessingService>> _mockFileProcessingLogger;
    private readonly Mock<IMemoryCache> _mockMemoryCache;
    private readonly TenantId _tenantId;

    public FileProcessingServiceTests()
    {
        _mockUploadRepository = new Mock<IPolicyMemberUploadRepository>();
        _mockValidationErrorExporterService = new Mock<IPolicyMemberUploadValidationErrorExporterService>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockFileSystemService = new Mock<IFileSystemService>();
        _mockFileParserFactory = new Mock<IFileParserFactory>();
        _mockFileParser = new Mock<IFileParser>();
        _mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockProductService = new Mock<IProductService>();
        _mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        _mockConfiguration = new Mock<IConfiguration>();
        var mockSection = new Mock<IConfigurationSection>();
        mockSection.Setup(s => s.Value).Returns("10");
        _mockConfiguration.Setup(c => c.GetSection("ValidationSettings:MaxConcurrentValidations")).Returns(mockSection.Object);

        // Create real specification instances with mocked dependencies

        // Create real instances of specifications with mock dependencies
        var uploadUniqueEmailsSpec = new UploadMustHaveUniqueEmailsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueEmailsSpecification>>());
        var uploadUniqueIdSpec = new UploadMustHaveUniqueIdentificationSpecification(Mock.Of<ILogger<UploadMustHaveUniqueIdentificationSpecification>>());
        var uploadUniqueMemberIdsSpec = new UploadMustHaveUniqueMemberIdsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>());
        var dependentPlanSpec = new DependentAndEmployeeMustBeOnSamePlanSpecification(Mock.Of<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>());
        // Setup mock for IUploadValidationOrchestrator to return empty errors dictionary
        var mockUploadValidationOrchestrator = new Mock<IUploadValidationOrchestrator>();
        mockUploadValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
            It.IsAny<UploadWideValidationContext>(),
            It.IsAny<UploadValidationSpecs>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var uploadWideSpec = new UploadWideValidationSpecification(
            uploadUniqueEmailsSpec,
            uploadUniqueIdSpec,
            uploadUniqueMemberIdsSpec,
            dependentPlanSpec,
            mockUploadValidationOrchestrator.Object,
            Mock.Of<ILogger<UploadWideValidationSpecification>>());

        var memberUniqueEmailSpec = new MemberMustHaveUniqueEmailSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>());
        var memberUniqueHkidSpec = new MemberMustHaveUniqueHKIDSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>());
        var memberUniquePassportSpec = new MemberMustHaveUniquePassportSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>());
        var memberUniqueStaffSpec = new MemberMustHaveUniqueStaffNumberSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>());
        var memberIdBusinessRulesSpec = new MemberIdMustFollowBusinessRulesSpecification(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>());
        var memberFieldsSchemaSpec = new MemberFieldsMustMatchSchemaSpecification(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>());
        var memberEffectiveDateSpec = new MemberEffectiveDateMustBeValidSpecification(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>());
        var dependentValidationSpec = new DependentMustHaveValidPrimaryMemberSpecification(Mock.Of<IPolicyMemberQueryService>(), Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>());
        var memberValidPlanIdSpec = new MemberMustHaveValidPlanIdSpecification(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>());

        // Setup mock for IConcurrentMemberProcessor to return empty errors dictionary
        var mockConcurrentMemberProcessor = new Mock<IConcurrentMemberProcessor>();
        mockConcurrentMemberProcessor.Setup(x => x.ProcessMembersAsync(
            It.IsAny<int>(),
            It.IsAny<Func<int, Task<List<ValidationError>>>>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var individualMemberSpec = new IndividualMemberValidationSpecification(
            memberUniqueEmailSpec,
            memberUniqueHkidSpec,
            memberUniquePassportSpec,
            memberUniqueStaffSpec,
            memberIdBusinessRulesSpec,
            memberFieldsSchemaSpec,
            memberEffectiveDateSpec,
            dependentValidationSpec,
            memberValidPlanIdSpec,
            mockConcurrentMemberProcessor.Object,
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());

        // Setup mock for IValidationErrorAggregator to return valid BatchValidationResult
        var mockErrorAggregator = new Mock<IValidationErrorAggregator>();
        mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns((List<BatchValidationResult> results, int _) =>
            {
                int totalValid = results.Sum(r => r.ValidCount);
                int totalInvalid = results.Sum(r => r.InvalidCount);
                var allErrors = results.SelectMany(r => r.RowErrors).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                return BatchValidationResult.WithErrors(totalValid, totalInvalid, allErrors);
            });

        _mockCompleteValidationSpec = new Mock<CompleteUploadValidationSpecification>(
            uploadWideSpec,
            individualMemberSpec,
            mockErrorAggregator.Object,
            Mock.Of<ILogger<CompleteUploadValidationSpecification>>());

        // Setup the ValidateAsync method to return a successful result by default
        _mockCompleteValidationSpec.Setup(x => x.ValidateAsync(It.IsAny<CompleteValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(ValidationOrchestrationResult.Success(0, 0, []));
        _mockUsersService = new Mock<IUsersService>();
        _mockLogger = new Mock<ILogger<ValidatePolicyMemberUploadHandler>>();
        _mockFileProcessingLogger = new Mock<ILogger<FileProcessingService>>();
        _mockMemoryCache = new Mock<IMemoryCache>();
        _tenantId = new TenantId("test-tenant");

        // Setup memory cache mock
        var mockCacheEntry = new Mock<ICacheEntry>();
        _mockMemoryCache.Setup(x => x.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);
        _mockMemoryCache.Setup(x => x.TryGetValue(It.IsAny<object>(), out It.Ref<object?>.IsAny)).Returns(false);

        // Setup policy member query service
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMemberCurrentStateAsync(It.IsAny<string>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMember?)null);

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
    }

    /// <summary>
    /// Helper method to create ValidatePolicyMemberUploadHandler with all required dependencies
    /// </summary>
    private ValidatePolicyMemberUploadHandler CreateHandler(IFileProcessingService fileProcessingService)
    {
        // Create mock for PolicyMemberValidationDataService
        var mockValidationDataService = new Mock<PolicyMemberValidationDataService>(
    _mockLegacyPolicyService.Object,
    _mockProductService.Object,
    _mockFeatureManager.Object,
    _tenantId,
    _mockSchemaProvider.Object,
    new Mock<ILogger<PolicyMemberValidationDataService>>().Object);

        return new ValidatePolicyMemberUploadHandler(
            _mockUploadRepository.Object,
            _mockValidationErrorExporterService.Object,
            _mockLegacyPolicyService.Object,
            _mockCompleteValidationSpec.Object,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            fileProcessingService,
            _mockLogger.Object,
            mockValidationDataService.Object);
    }

    [Fact]
    public async Task FileProcessing_WithValidCsvFile_ShouldProduceIdenticalResults()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        byte[] csvContent = CreateValidCsvContent();
        FileParseResult parseResult = CreateValidParseResult();

        // Setup file processing mocks
        SetupFileProcessingMocks(csvContent, parseResult);
        SetupCommonMocks(upload, policy, schema);

        // Create handler with FileProcessingService
        var fileProcessingService = new FileProcessingService(
            _mockFileSystemService.Object,
            _mockFileParserFactory.Object,
            _mockFileProcessingLogger.Object);

        ValidatePolicyMemberUploadHandler handler = CreateHandler(fileProcessingService);

        var command = new ValidatePolicyMemberUploadCommand
        {
            PolicyId = Guid.Parse(policy.Id),
            UploadId = upload.Id.Value
        };

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await handler.Handle(command, CancellationToken.None);

        // Assert - Verify the handler processes the file successfully
        if (!result.IsSuccess)
        {
            // Debug output to understand why the test is failing
            string errors = string.Join(", ", result.Errors.Select(e => e.Message));
            throw new Exception($"Handler failed with errors: {errors}");
        }
        Assert.True(result.IsSuccess);

        // Verify file system service was called correctly
        _mockFileSystemService.Verify(x => x.GetFileByPath(upload.Path, It.IsAny<CancellationToken>()), Times.Once);

        // Verify file parser was used correctly
        _mockFileParserFactory.Verify(x => x.CreateParser(csvContent), Times.Once);
        _mockFileParser.Verify(x => x.ParseFileAsync(csvContent, It.IsAny<CancellationToken>()), Times.Once);

        // Verify the upload was processed (allow multiple calls due to handler logic)
        _mockUploadRepository.Verify(x => x.StartValidationIfNotLockedAsync(upload.Id, It.IsAny<CancellationToken>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task FileProcessing_WithFileNotFound_ShouldThrowUploadFileNotFoundException()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateValidSchema();

        // Setup file not found scenario
        _mockFileSystemService.Setup(x => x.GetFileByPath(upload.Path, It.IsAny<CancellationToken>()))
            .ReturnsAsync((byte[]?)null);

        SetupCommonMocks(upload, policy, schema);

        var fileProcessingService = new FileProcessingService(
            _mockFileSystemService.Object,
            _mockFileParserFactory.Object,
            _mockFileProcessingLogger.Object);

        ValidatePolicyMemberUploadHandler handler = CreateHandler(fileProcessingService);

        var command = new ValidatePolicyMemberUploadCommand
        {
            PolicyId = Guid.Parse(policy.Id),
            UploadId = upload.Id.Value
        };

        // Act - Handler should catch domain exceptions and return failure result
        Result<ValidatePolicyMemberUploadResponse> result = await handler.Handle(command, CancellationToken.None);

        // Assert - Should return failure with UPLOAD_FILE_NOT_FOUND error
        Assert.True(result.IsFailure);
        Assert.Single(result.Errors);
        Assert.Equal("UPLOAD_FILE_NOT_FOUND", result.Errors.First().Code);
    }

    [Fact]
    public async Task FileProcessing_WithParsingError_ShouldThrowOriginalException()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateValidSchema();
        byte[] invalidContent = Encoding.UTF8.GetBytes("invalid,content");
        var parseException = new InvalidOperationException("Parse failed");

        // Setup parsing error scenario
        _mockFileSystemService.Setup(x => x.GetFileByPath(upload.Path, It.IsAny<CancellationToken>()))
            .ReturnsAsync(invalidContent);
        _mockFileParserFactory.Setup(x => x.CreateParser(invalidContent))
            .Returns(_mockFileParser.Object);
        _mockFileParser.Setup(x => x.ParseFileAsync(invalidContent, It.IsAny<CancellationToken>()))
            .ThrowsAsync(parseException);

        SetupCommonMocks(upload, policy, schema);

        var fileProcessingService = new FileProcessingService(
            _mockFileSystemService.Object,
            _mockFileParserFactory.Object,
            _mockFileProcessingLogger.Object);

        ValidatePolicyMemberUploadHandler handler = CreateHandler(fileProcessingService);

        var command = new ValidatePolicyMemberUploadCommand
        {
            PolicyId = policy.Id,
            UploadId = upload.Id
        };

        // Act & Assert - Should throw the same exception as the original implementation
        InvalidOperationException exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            handler.Handle(command, CancellationToken.None));

        Assert.Equal("Parse failed", exception.Message);
    }

    #region Helper Methods

    private void SetupFileProcessingMocks(byte[] fileContent, FileParseResult parseResult)
    {
        _mockFileSystemService.Setup(x => x.GetFileByPath(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(fileContent);
        _mockFileParserFactory.Setup(x => x.CreateParser(fileContent))
            .Returns(_mockFileParser.Object);
        _mockFileParser.Setup(x => x.ParseFileAsync(fileContent, It.IsAny<CancellationToken>()))
            .ReturnsAsync(parseResult);
    }

    private void SetupCommonMocks(PolicyMemberUpload upload, PolicyDto policy, PolicyMemberFieldsSchema schema)
    {
        // Setup repository mock
        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(upload.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _mockUploadRepository.Setup(x => x.UpdateAsync(It.IsAny<PolicyMemberUpload>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        // Setup legacy policy service mock
        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        // Setup schema provider mock
        // Use the appropriate method from the interface
        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(
            It.IsAny<string>(),
            It.IsAny<ProductId>(),
            It.IsAny<EndorsementId?>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);
    }

    private static PolicyMemberUpload CreateValidUpload() => PolicyMemberUpload.Create(PolicyId.New, "uploads/test-upload.csv", 10);

    private static PolicyDto CreateValidPolicy()
    {
        string policyId = Guid.NewGuid().ToString();
        return new PolicyDto
        {
            Id = policyId,
            ContractHolderId = "CH001",
            ProductId = new ProductIdDto { Plan = "plan", Type = "type", Version = "version" },
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(330)),
            Endorsements = []
        };
    }

    private static PolicyMemberFieldsSchema CreateValidSchema() => new([]);

    private static byte[] CreateValidCsvContent() => Encoding.UTF8.GetBytes("Name,Age,Email\nJohn Doe,30,<EMAIL>\nJane Smith,25,<EMAIL>");

    private static FileParseResult CreateValidParseResult() => new()
    {
        Headers = ["Name", "Age", "Email"],
        Contents = new List<Dictionary<string, string?>>
                {
                    new() { { "Name", "John Doe" }, { "Age", "30" }, { "Email", "<EMAIL>" } },
                    new() { { "Name", "Jane Smith" }, { "Age", "25" }, { "Email", "<EMAIL>" } }
                }
    };

    #endregion
}